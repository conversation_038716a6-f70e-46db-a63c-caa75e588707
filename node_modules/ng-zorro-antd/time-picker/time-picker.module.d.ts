import * as i0 from "@angular/core";
import * as i1 from "./time-picker.component";
import * as i2 from "./time-picker-panel.component";
export declare class NzTimePickerModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTimePickerModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzTimePickerModule, never, [typeof i1.NzTimePickerComponent, typeof i2.NzTimePickerPanelComponent], [typeof i2.NzTimePickerPanelComponent, typeof i1.NzTimePickerComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzTimePickerModule>;
}
