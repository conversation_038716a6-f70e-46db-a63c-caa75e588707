/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { Direction, Directionality } from '@angular/cdk/bidi';
import { AfterContentInit, ChangeDetectorRef, OnChanges, OnDestroy, OnInit, QueryList, SimpleChanges, TemplateRef } from '@angular/core';
import { NzTimelineItemComponent } from './timeline-item.component';
import { TimelineService } from './timeline.service';
import { NzTimelineMode } from './typings';
import * as i0 from "@angular/core";
export declare class NzTimelineComponent implements AfterContentInit, OnChanges, OnDestroy, OnInit {
    private cdr;
    private timelineService;
    private directionality;
    listOfItems: QueryList<NzTimelineItemComponent>;
    nzMode: NzTimelineMode;
    nzPending?: string | boolean | TemplateRef<void>;
    nzPendingDot?: string | TemplateRef<void>;
    nzReverse: boolean;
    isPendingBoolean: boolean;
    timelineItems: NzTimelineItemComponent[];
    dir: Direction;
    hasLabelItem: boolean;
    private destroy$;
    constructor(cdr: ChangeDetectorRef, timelineService: TimelineService, directionality: Directionality);
    ngOnChanges(changes: SimpleChanges): void;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    ngOnDestroy(): void;
    private updateChildren;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTimelineComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTimelineComponent, "nz-timeline", ["nzTimeline"], { "nzMode": { "alias": "nzMode"; "required": false; }; "nzPending": { "alias": "nzPending"; "required": false; }; "nzPendingDot": { "alias": "nzPendingDot"; "required": false; }; "nzReverse": { "alias": "nzReverse"; "required": false; }; }, {}, ["listOfItems"], ["*"], true, never>;
    static ngAcceptInputType_nzReverse: unknown;
}
