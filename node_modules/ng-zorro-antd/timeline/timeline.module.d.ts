import * as i0 from "@angular/core";
import * as i1 from "./timeline-item.component";
import * as i2 from "./timeline.component";
export declare class NzTimelineModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTimelineModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzTimelineModule, never, [typeof i1.NzTimelineItemComponent, typeof i2.NzTimelineComponent], [typeof i1.NzTimelineItemComponent, typeof i2.NzTimelineComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzTimelineModule>;
}
