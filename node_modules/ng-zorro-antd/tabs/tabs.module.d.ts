import * as i0 from "@angular/core";
import * as i1 from "./tabset.component";
import * as i2 from "./tab.component";
import * as i3 from "./tab-nav-bar.component";
import * as i4 from "./tab-nav-item.directive";
import * as i5 from "./tabs-ink-bar.directive";
import * as i6 from "./tab-scroll-list.directive";
import * as i7 from "./tab-nav-operation.component";
import * as i8 from "./tab-add-button.component";
import * as i9 from "./tab-close-button.component";
import * as i10 from "./tab.directive";
import * as i11 from "./tab-body.component";
import * as i12 from "./tab-link.directive";
import * as i13 from "./tab-bar-extra-content.directive";
export declare class NzTabsModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTabsModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzTabsModule, never, [typeof i1.NzTabSetComponent, typeof i2.NzTabComponent, typeof i3.NzTabNavBarComponent, typeof i4.NzTabNavItemDirective, typeof i5.NzTabsInkBarDirective, typeof i6.NzTabScrollListDirective, typeof i7.NzTabNavOperationComponent, typeof i8.NzTabAddButtonComponent, typeof i9.NzTabCloseButtonComponent, typeof i10.NzTabDirective, typeof i11.NzTabBodyComponent, typeof i12.NzTabLinkDirective, typeof i12.NzTabLinkTemplateDirective, typeof i13.NzTabBarExtraContentDirective], [typeof i1.NzTabSetComponent, typeof i2.NzTabComponent, typeof i3.NzTabNavBarComponent, typeof i4.NzTabNavItemDirective, typeof i5.NzTabsInkBarDirective, typeof i6.NzTabScrollListDirective, typeof i7.NzTabNavOperationComponent, typeof i8.NzTabAddButtonComponent, typeof i9.NzTabCloseButtonComponent, typeof i10.NzTabDirective, typeof i11.NzTabBodyComponent, typeof i12.NzTabLinkDirective, typeof i12.NzTabLinkTemplateDirective, typeof i13.NzTabBarExtraContentDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzTabsModule>;
}
