/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { TemplateRef } from '@angular/core';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import * as i0 from "@angular/core";
export declare class NzTabCloseButtonComponent {
    closeIcon: string | TemplateRef<NzSafeAny>;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTabCloseButtonComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTabCloseButtonComponent, "nz-tab-close-button, button[nz-tab-close-button]", never, { "closeIcon": { "alias": "closeIcon"; "required": false; }; }, {}, never, never, true, never>;
}
