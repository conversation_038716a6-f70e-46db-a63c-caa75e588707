/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { FocusableOption } from '@angular/cdk/a11y';
import { ElementRef } from '@angular/core';
import { NzTabComponent } from './tab.component';
import * as i0 from "@angular/core";
export declare class NzTabNavItemDirective implements FocusableOption {
    elementRef: ElementRef<HTMLElement>;
    disabled: boolean;
    tab: NzTabComponent;
    active: boolean;
    private el;
    private parentElement;
    constructor(elementRef: ElementRef<HTMLElement>);
    focus(): void;
    get width(): number;
    get height(): number;
    get left(): number;
    get top(): number;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTabNavItemDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<NzTabNavItemDirective, "[nzTabNavItem]", never, { "disabled": { "alias": "disabled"; "required": false; }; "tab": { "alias": "tab"; "required": false; }; "active": { "alias": "active"; "required": false; }; }, {}, never, never, true, never>;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_active: unknown;
}
