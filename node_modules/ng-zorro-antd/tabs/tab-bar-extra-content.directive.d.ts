/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { TemplateRef } from '@angular/core';
import * as i0 from "@angular/core";
export declare class NzTabBarExtraContentDirective {
    readonly position: import("@angular/core").InputSignal<"start" | "end">;
    readonly templateRef: TemplateRef<any>;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTabBarExtraContentDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<NzTabBarExtraContentDirective, "[nzTabBarExtraContent]:not(nz-tabset)", never, { "position": { "alias": "nzTabBarExtraContent"; "required": false; "isSignal": true; }; }, {}, never, never, true, never>;
}
