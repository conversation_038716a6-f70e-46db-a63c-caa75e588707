import { Direction, Directionality } from '@angular/cdk/bidi';
import { AfterContentChecked, AfterContentInit, ChangeDetectorRef, EventEmitter, NgZone, OnDestroy, OnInit, QueryList, TemplateRef } from '@angular/core';
import { Observable } from 'rxjs';
import { NzConfigKey, NzConfigService } from 'ng-zorro-antd/core/config';
import { NzSafeAny, NzSizeLDSType } from 'ng-zorro-antd/core/types';
import { NzAnimatedInterface, NzTabChangeEvent, NzTabPosition, NzTabPositionMode, NzTabsCanDeactivateFn, NzTabScrollEvent, NzTabType } from './interfaces';
import { NzTabBarExtraContentDirective } from './tab-bar-extra-content.directive';
import { NzTabLinkDirective } from './tab-link.directive';
import { NzTabNavBarComponent } from './tab-nav-bar.component';
import { NzTabComponent } from './tab.component';
import * as i0 from "@angular/core";
export declare class NzTabSetComponent implements OnInit, AfterContentChecked, OnDestroy, AfterContentInit {
    nzConfigService: NzConfigService;
    private ngZone;
    private cdr;
    private directionality;
    readonly _nzModuleName: NzConfigKey;
    get nzSelectedIndex(): number | null;
    set nzSelectedIndex(value: null | number);
    nzTabPosition: NzTabPosition;
    nzTabBarExtraContent?: TemplateRef<void>;
    nzCanDeactivate: NzTabsCanDeactivateFn | null;
    nzAddIcon: string | TemplateRef<NzSafeAny>;
    nzTabBarStyle: Record<string, string> | null;
    nzType: NzTabType;
    nzSize: NzSizeLDSType;
    nzAnimated: NzAnimatedInterface | boolean;
    nzTabBarGutter?: number;
    nzHideAdd: boolean;
    nzCentered: boolean;
    nzHideAll: boolean;
    nzLinkRouter: boolean;
    nzLinkExact: boolean;
    nzDestroyInactiveTabPane: boolean;
    readonly nzSelectChange: EventEmitter<NzTabChangeEvent>;
    readonly nzSelectedIndexChange: EventEmitter<number>;
    readonly nzTabListScroll: EventEmitter<NzTabScrollEvent>;
    readonly nzClose: EventEmitter<{
        index: number;
    }>;
    readonly nzAdd: EventEmitter<void>;
    get position(): NzTabPositionMode;
    get addable(): boolean;
    get closable(): boolean;
    get line(): boolean;
    get inkBarAnimated(): boolean;
    get tabPaneAnimated(): boolean;
    allTabs: QueryList<NzTabComponent>;
    tabLinks: QueryList<NzTabLinkDirective>;
    tabNavBarRef: NzTabNavBarComponent;
    tabs: QueryList<NzTabComponent>;
    readonly extraContents: import("@angular/core").Signal<readonly NzTabBarExtraContentDirective[]>;
    dir: Direction;
    private readonly tabSetId;
    private destroy$;
    private indexToSelect;
    private selectedIndex;
    private tabLabelSubscription;
    private tabsSubscription;
    private canDeactivateSubscription;
    private router;
    constructor(nzConfigService: NzConfigService, ngZone: NgZone, cdr: ChangeDetectorRef, directionality: Directionality);
    ngOnInit(): void;
    ngOnDestroy(): void;
    ngAfterContentInit(): void;
    ngAfterContentChecked(): void;
    onClose(index: number, e: MouseEvent): void;
    onAdd(): void;
    private clampTabIndex;
    private createChangeEvent;
    private subscribeToTabLabels;
    private subscribeToAllTabChanges;
    canDeactivateFun(pre: number, next: number): Observable<boolean>;
    clickNavItem(tab: NzTabComponent, index: number, e: MouseEvent): void;
    private isRouterLinkClickEvent;
    contextmenuNavItem(tab: NzTabComponent, e: MouseEvent): void;
    setSelectedIndex(index: number): void;
    getTabIndex(tab: NzTabComponent, index: number): number | null;
    getTabContentId(i: number): string;
    private setUpRouter;
    private updateRouterActive;
    private findShouldActiveTabIndex;
    private isLinkActive;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTabSetComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTabSetComponent, "nz-tabset", ["nzTabset"], { "nzSelectedIndex": { "alias": "nzSelectedIndex"; "required": false; }; "nzTabPosition": { "alias": "nzTabPosition"; "required": false; }; "nzTabBarExtraContent": { "alias": "nzTabBarExtraContent"; "required": false; }; "nzCanDeactivate": { "alias": "nzCanDeactivate"; "required": false; }; "nzAddIcon": { "alias": "nzAddIcon"; "required": false; }; "nzTabBarStyle": { "alias": "nzTabBarStyle"; "required": false; }; "nzType": { "alias": "nzType"; "required": false; }; "nzSize": { "alias": "nzSize"; "required": false; }; "nzAnimated": { "alias": "nzAnimated"; "required": false; }; "nzTabBarGutter": { "alias": "nzTabBarGutter"; "required": false; }; "nzHideAdd": { "alias": "nzHideAdd"; "required": false; }; "nzCentered": { "alias": "nzCentered"; "required": false; }; "nzHideAll": { "alias": "nzHideAll"; "required": false; }; "nzLinkRouter": { "alias": "nzLinkRouter"; "required": false; }; "nzLinkExact": { "alias": "nzLinkExact"; "required": false; }; "nzDestroyInactiveTabPane": { "alias": "nzDestroyInactiveTabPane"; "required": false; }; }, { "nzSelectChange": "nzSelectChange"; "nzSelectedIndexChange": "nzSelectedIndexChange"; "nzTabListScroll": "nzTabListScroll"; "nzClose": "nzClose"; "nzAdd": "nzAdd"; }, ["extraContents", "allTabs", "tabLinks"], never, true, never>;
    static ngAcceptInputType_nzHideAdd: unknown;
    static ngAcceptInputType_nzCentered: unknown;
    static ngAcceptInputType_nzHideAll: unknown;
    static ngAcceptInputType_nzLinkRouter: unknown;
    static ngAcceptInputType_nzLinkExact: unknown;
    static ngAcceptInputType_nzDestroyInactiveTabPane: unknown;
}
