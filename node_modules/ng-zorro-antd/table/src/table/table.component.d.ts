/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { Direction, Directionality } from '@angular/cdk/bidi';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { AfterViewInit, ChangeDetectorRef, ElementRef, EventEmitter, OnChanges, OnDestroy, OnInit, SimpleChanges, TemplateRef, TrackByFunction } from '@angular/core';
import { NzResizeObserver } from 'ng-zorro-antd/cdk/resize-observer';
import { NzConfigKey, NzConfigService } from 'ng-zorro-antd/core/config';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { PaginationItemRenderContext } from 'ng-zorro-antd/pagination';
import { NzTableDataService } from '../table-data.service';
import { NzTableStyleService } from '../table-style.service';
import { NzCustomColumn, NzTableLayout, NzTablePaginationPosition, NzTablePaginationType, NzTableQueryParams, NzTableSize, NzTableSummaryFixedType } from '../table.types';
import { NzTableInnerScrollComponent } from './table-inner-scroll.component';
import { NzTableVirtualScrollDirective } from './table-virtual-scroll.directive';
import * as i0 from "@angular/core";
export declare class NzTableComponent<T> implements OnInit, OnDestroy, OnChanges, AfterViewInit {
    private elementRef;
    private nzResizeObserver;
    private nzConfigService;
    private cdr;
    private nzTableStyleService;
    private nzTableDataService;
    private directionality;
    readonly _nzModuleName: NzConfigKey;
    nzTableLayout: NzTableLayout;
    nzShowTotal: TemplateRef<{
        $implicit: number;
        range: [number, number];
    }> | null;
    nzItemRender: TemplateRef<PaginationItemRenderContext> | null;
    nzTitle: string | TemplateRef<NzSafeAny> | null;
    nzFooter: string | TemplateRef<NzSafeAny> | null;
    nzNoResult: string | TemplateRef<NzSafeAny> | undefined;
    nzPageSizeOptions: number[];
    nzVirtualItemSize: number;
    nzVirtualMaxBufferPx: number;
    nzVirtualMinBufferPx: number;
    nzVirtualForTrackBy: TrackByFunction<T>;
    nzLoadingDelay: number;
    nzPageIndex: number;
    nzPageSize: number;
    nzTotal: number;
    nzWidthConfig: ReadonlyArray<string | null>;
    nzData: readonly T[];
    nzCustomColumn: NzCustomColumn[];
    nzPaginationPosition: NzTablePaginationPosition;
    nzScroll: {
        x?: string | null;
        y?: string | null;
    };
    noDataVirtualHeight: string;
    nzPaginationType: NzTablePaginationType;
    nzFrontPagination: boolean;
    nzTemplateMode: boolean;
    nzShowPagination: boolean;
    nzLoading: boolean;
    nzOuterBordered: boolean;
    nzLoadingIndicator: TemplateRef<NzSafeAny> | null;
    nzBordered: boolean;
    nzSize: NzTableSize;
    nzShowSizeChanger: boolean;
    nzHideOnSinglePage: boolean;
    nzShowQuickJumper: boolean;
    nzSimple: boolean;
    readonly nzPageSizeChange: EventEmitter<number>;
    readonly nzPageIndexChange: EventEmitter<number>;
    readonly nzQueryParams: EventEmitter<NzTableQueryParams>;
    readonly nzCurrentPageDataChange: EventEmitter<readonly T[]>;
    readonly nzCustomColumnChange: EventEmitter<readonly NzCustomColumn[]>;
    /** public data for ngFor tr */
    data: readonly T[];
    cdkVirtualScrollViewport?: CdkVirtualScrollViewport;
    scrollX: string | null;
    scrollY: string | null;
    theadTemplate: TemplateRef<NzSafeAny> | null;
    tfootTemplate: TemplateRef<NzSafeAny> | null;
    tfootFixed: NzTableSummaryFixedType | null;
    listOfAutoColWidth: ReadonlyArray<string | null>;
    listOfManualColWidth: ReadonlyArray<string | null>;
    hasFixLeft: boolean;
    hasFixRight: boolean;
    showPagination: boolean;
    private destroy$;
    private templateMode$;
    dir: Direction;
    nzVirtualScrollDirective: NzTableVirtualScrollDirective<T>;
    nzTableInnerScrollComponent: NzTableInnerScrollComponent<T>;
    verticalScrollBarWidth: number;
    onPageSizeChange(size: number): void;
    onPageIndexChange(index: number): void;
    constructor(elementRef: ElementRef, nzResizeObserver: NzResizeObserver, nzConfigService: NzConfigService, cdr: ChangeDetectorRef, nzTableStyleService: NzTableStyleService, nzTableDataService: NzTableDataService<T>, directionality: Directionality);
    ngOnInit(): void;
    ngOnChanges(changes: SimpleChanges): void;
    ngAfterViewInit(): void;
    ngOnDestroy(): void;
    private setScrollOnChanges;
    private updateShowPagination;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTableComponent<any>, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTableComponent<any>, "nz-table", ["nzTable"], { "nzTableLayout": { "alias": "nzTableLayout"; "required": false; }; "nzShowTotal": { "alias": "nzShowTotal"; "required": false; }; "nzItemRender": { "alias": "nzItemRender"; "required": false; }; "nzTitle": { "alias": "nzTitle"; "required": false; }; "nzFooter": { "alias": "nzFooter"; "required": false; }; "nzNoResult": { "alias": "nzNoResult"; "required": false; }; "nzPageSizeOptions": { "alias": "nzPageSizeOptions"; "required": false; }; "nzVirtualItemSize": { "alias": "nzVirtualItemSize"; "required": false; }; "nzVirtualMaxBufferPx": { "alias": "nzVirtualMaxBufferPx"; "required": false; }; "nzVirtualMinBufferPx": { "alias": "nzVirtualMinBufferPx"; "required": false; }; "nzVirtualForTrackBy": { "alias": "nzVirtualForTrackBy"; "required": false; }; "nzLoadingDelay": { "alias": "nzLoadingDelay"; "required": false; }; "nzPageIndex": { "alias": "nzPageIndex"; "required": false; }; "nzPageSize": { "alias": "nzPageSize"; "required": false; }; "nzTotal": { "alias": "nzTotal"; "required": false; }; "nzWidthConfig": { "alias": "nzWidthConfig"; "required": false; }; "nzData": { "alias": "nzData"; "required": false; }; "nzCustomColumn": { "alias": "nzCustomColumn"; "required": false; }; "nzPaginationPosition": { "alias": "nzPaginationPosition"; "required": false; }; "nzScroll": { "alias": "nzScroll"; "required": false; }; "noDataVirtualHeight": { "alias": "noDataVirtualHeight"; "required": false; }; "nzPaginationType": { "alias": "nzPaginationType"; "required": false; }; "nzFrontPagination": { "alias": "nzFrontPagination"; "required": false; }; "nzTemplateMode": { "alias": "nzTemplateMode"; "required": false; }; "nzShowPagination": { "alias": "nzShowPagination"; "required": false; }; "nzLoading": { "alias": "nzLoading"; "required": false; }; "nzOuterBordered": { "alias": "nzOuterBordered"; "required": false; }; "nzLoadingIndicator": { "alias": "nzLoadingIndicator"; "required": false; }; "nzBordered": { "alias": "nzBordered"; "required": false; }; "nzSize": { "alias": "nzSize"; "required": false; }; "nzShowSizeChanger": { "alias": "nzShowSizeChanger"; "required": false; }; "nzHideOnSinglePage": { "alias": "nzHideOnSinglePage"; "required": false; }; "nzShowQuickJumper": { "alias": "nzShowQuickJumper"; "required": false; }; "nzSimple": { "alias": "nzSimple"; "required": false; }; }, { "nzPageSizeChange": "nzPageSizeChange"; "nzPageIndexChange": "nzPageIndexChange"; "nzQueryParams": "nzQueryParams"; "nzCurrentPageDataChange": "nzCurrentPageDataChange"; "nzCustomColumnChange": "nzCustomColumnChange"; }, ["nzVirtualScrollDirective"], ["*"], true, never>;
    static ngAcceptInputType_nzFrontPagination: unknown;
    static ngAcceptInputType_nzTemplateMode: unknown;
    static ngAcceptInputType_nzShowPagination: unknown;
    static ngAcceptInputType_nzLoading: unknown;
    static ngAcceptInputType_nzOuterBordered: unknown;
    static ngAcceptInputType_nzBordered: unknown;
    static ngAcceptInputType_nzShowSizeChanger: unknown;
    static ngAcceptInputType_nzHideOnSinglePage: unknown;
    static ngAcceptInputType_nzShowQuickJumper: unknown;
    static ngAcceptInputType_nzSimple: unknown;
}
