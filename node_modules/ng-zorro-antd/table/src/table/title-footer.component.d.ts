/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { TemplateRef } from '@angular/core';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import * as i0 from "@angular/core";
export declare class NzTableTitleFooterComponent {
    title: string | TemplateRef<NzSafeAny> | null;
    footer: string | TemplateRef<NzSafeAny> | null;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTableTitleFooterComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTableTitleFooterComponent, "nz-table-title-footer", never, { "title": { "alias": "title"; "required": false; }; "footer": { "alias": "footer"; "required": false; }; }, {}, never, never, true, never>;
}
