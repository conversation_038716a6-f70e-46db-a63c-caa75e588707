import * as i0 from "@angular/core";
export declare class NzCellBreakWordDirective {
    nzBreakWord: boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzCellBreakWordDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<NzCellBreakWordDirective, "th[nzBreakWord],td[nzBreakWord]", never, { "nzBreakWord": { "alias": "nzBreakWord"; "required": false; }; }, {}, never, never, true, never>;
    static ngAcceptInputType_nzBreakWord: unknown;
}
