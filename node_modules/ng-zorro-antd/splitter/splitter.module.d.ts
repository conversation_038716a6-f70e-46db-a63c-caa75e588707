import * as i0 from "@angular/core";
import * as i1 from "./splitter.component";
import * as i2 from "./splitter-panel.component";
export declare class NzSplitterModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzSplitterModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzSplitterModule, never, [typeof i1.NzSplitterComponent, typeof i2.NzSplitterPanelComponent], [typeof i1.NzSplitterComponent, typeof i2.NzSplitterPanelComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzSplitterModule>;
}
