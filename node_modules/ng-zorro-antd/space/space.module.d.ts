import * as i0 from "@angular/core";
import * as i1 from "./space.component";
import * as i2 from "./space-item.directive";
import * as i3 from "./space-compact.component";
export declare class NzSpaceModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzSpaceModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzSpaceModule, never, [typeof i1.NzSpaceComponent, typeof i2.NzSpaceItemDirective, typeof i3.NzSpaceCompactComponent], [typeof i1.NzSpaceComponent, typeof i2.NzSpaceItemDirective, typeof i3.NzSpaceCompactComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzSpaceModule>;
}
