import * as i0 from "@angular/core";
import * as i1 from "./upload.component";
import * as i2 from "./upload-btn.component";
import * as i3 from "./upload-list.component";
export declare class NzUploadModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzUploadModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzUploadModule, never, [typeof i1.NzUploadComponent, typeof i2.NzUploadBtnComponent, typeof i3.NzUploadListComponent], [typeof i1.NzUploadComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzUploadModule>;
}
