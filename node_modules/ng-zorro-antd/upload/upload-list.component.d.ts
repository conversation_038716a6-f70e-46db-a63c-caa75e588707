import { Direction } from '@angular/cdk/bidi';
import { Platform } from '@angular/cdk/platform';
import { ChangeDetectorRef, NgZone, OnChanges, OnDestroy } from '@angular/core';
import { Observable } from 'rxjs';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzIconRenderTemplate, NzShowUploadList, NzUploadFile, NzUploadListType } from './interface';
import * as i0 from "@angular/core";
type UploadListIconType = '' | 'uploading' | 'thumbnail';
interface UploadListFile extends NzUploadFile {
    isImageUrl?: boolean;
    isUploading?: boolean;
    iconType?: UploadListIconType;
    showDownload?: boolean;
}
export declare class NzUploadListComponent implements OnChanges, OnDestroy {
    private cdr;
    private ngZone;
    private platform;
    list: UploadListFile[];
    private get showPic();
    locale: NzSafeAny;
    listType: NzUploadListType;
    set items(list: NzUploadFile[]);
    icons: NzShowUploadList;
    onPreview?: (file: NzUploadFile) => void;
    onRemove: (file: NzUploadFile) => void;
    onDownload?: (file: NzUploadFile) => void;
    previewFile?: (file: NzUploadFile) => Observable<string>;
    previewIsImage?: (file: NzUploadFile) => boolean;
    iconRender: NzIconRenderTemplate | null;
    dir: Direction;
    private document;
    private destroy$;
    private genErr;
    private extname;
    isImageUrl(file: NzUploadFile): boolean;
    private getIconType;
    private previewImage;
    private genThumb;
    private showDownload;
    private fixData;
    handlePreview(file: NzUploadFile, e: Event): void;
    handleRemove(file: NzUploadFile, e: Event): void;
    handleDownload(file: NzUploadFile): void;
    constructor(cdr: ChangeDetectorRef, ngZone: NgZone, platform: Platform);
    detectChanges(): void;
    ngOnChanges(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzUploadListComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzUploadListComponent, "nz-upload-list", ["nzUploadList"], { "locale": { "alias": "locale"; "required": false; }; "listType": { "alias": "listType"; "required": false; }; "items": { "alias": "items"; "required": false; }; "icons": { "alias": "icons"; "required": false; }; "onPreview": { "alias": "onPreview"; "required": false; }; "onRemove": { "alias": "onRemove"; "required": false; }; "onDownload": { "alias": "onDownload"; "required": false; }; "previewFile": { "alias": "previewFile"; "required": false; }; "previewIsImage": { "alias": "previewIsImage"; "required": false; }; "iconRender": { "alias": "iconRender"; "required": false; }; "dir": { "alias": "dir"; "required": false; }; }, {}, never, never, true, never>;
}
export {};
