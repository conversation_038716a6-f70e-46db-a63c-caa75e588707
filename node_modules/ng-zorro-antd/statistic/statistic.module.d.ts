import * as i0 from "@angular/core";
import * as i1 from "./statistic.component";
import * as i2 from "./countdown.component";
import * as i3 from "./statistic-number.component";
export declare class NzStatisticModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzStatisticModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzStatisticModule, never, [typeof i1.NzStatisticComponent, typeof i2.NzCountdownComponent, typeof i3.NzStatisticNumberComponent], [typeof i1.NzStatisticComponent, typeof i2.NzCountdownComponent, typeof i3.NzStatisticNumberComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzStatisticModule>;
}
