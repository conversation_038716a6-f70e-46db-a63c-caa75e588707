import * as i0 from "@angular/core";
export declare class NzTreeNodeBuiltinCheckboxComponent {
    nzSelectMode: boolean;
    isChecked?: boolean;
    isHalfChecked?: boolean;
    isDisabled?: boolean;
    isDisableCheckbox?: boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTreeNodeBuiltinCheckboxComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTreeNodeBuiltinCheckboxComponent, "nz-tree-node-checkbox[builtin]", never, { "nzSelectMode": { "alias": "nzSelectMode"; "required": false; }; "isChecked": { "alias": "isChecked"; "required": false; }; "isHalfChecked": { "alias": "isHalfChecked"; "required": false; }; "isDisabled": { "alias": "isDisabled"; "required": false; }; "isDisableCheckbox": { "alias": "isDisableCheckbox"; "required": false; }; }, {}, never, never, true, never>;
    static ngAcceptInputType_isChecked: unknown;
    static ngAcceptInputType_isHalfChecked: unknown;
    static ngAcceptInputType_isDisabled: unknown;
    static ngAcceptInputType_isDisableCheckbox: unknown;
}
