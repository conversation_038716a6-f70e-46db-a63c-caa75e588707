import { AfterViewInit, ChangeDetectorRef, ElementRef, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { FontType, MarkStyleType } from './typings';
import * as i0 from "@angular/core";
export declare class NzWaterMarkComponent implements AfterViewInit, OnInit, OnChanges, OnDestroy {
    private el;
    private cdr;
    nzWidth: number;
    nzHeight: number;
    nzRotate: number;
    nzZIndex: number;
    nzImage: string;
    nzContent: string | string[];
    nzFont: FontType;
    nzGap: [number, number];
    nzOffset: [number, number];
    private document;
    waterMarkElement: HTMLDivElement;
    stopObservation: boolean;
    observer: MutationObserver;
    constructor(el: ElementRef, cdr: ChangeDetectorRef);
    ngOnInit(): void;
    ngAfterViewInit(): void;
    ngOnChanges(changes: SimpleChanges): void;
    getFont(): void;
    getMarkStyle(): MarkStyleType;
    destroyWatermark(): void;
    appendWatermark(base64Url: string, markWidth: number): void;
    getMarkSize(ctx: CanvasRenderingContext2D): [number, number];
    fillTexts(ctx: CanvasRenderingContext2D, drawX: number, drawY: number, drawWidth: number, drawHeight: number): void;
    drawText(canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D, drawX: number, drawY: number, drawWidth: number, drawHeight: number, alternateRotateX: number, alternateRotateY: number, alternateDrawX: number, alternateDrawY: number, markWidth: number): void;
    renderWatermark(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzWaterMarkComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzWaterMarkComponent, "nz-water-mark", ["NzWaterMark"], { "nzWidth": { "alias": "nzWidth"; "required": false; }; "nzHeight": { "alias": "nzHeight"; "required": false; }; "nzRotate": { "alias": "nzRotate"; "required": false; }; "nzZIndex": { "alias": "nzZIndex"; "required": false; }; "nzImage": { "alias": "nzImage"; "required": false; }; "nzContent": { "alias": "nzContent"; "required": false; }; "nzFont": { "alias": "nzFont"; "required": false; }; "nzGap": { "alias": "nzGap"; "required": false; }; "nzOffset": { "alias": "nzOffset"; "required": false; }; }, {}, never, ["*"], true, never>;
    static ngAcceptInputType_nzWidth: unknown;
    static ngAcceptInputType_nzHeight: unknown;
    static ngAcceptInputType_nzRotate: unknown;
    static ngAcceptInputType_nzZIndex: unknown;
}
