/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { Direction, Directionality } from '@angular/cdk/bidi';
import { ChangeDetectorRef, ElementRef, EventEmitter, OnChanges, OnDestroy, OnInit, QueryList, Renderer2, SimpleChanges, TemplateRef } from '@angular/core';
import { Observable } from 'rxjs';
import { NgClassInterface, NgStyleInterface, NzSafeAny, NzStatus } from 'ng-zorro-antd/core/types';
import { NzI18nService, NzTransferI18nInterface } from 'ng-zorro-antd/i18n';
import { TransferCanMove, TransferChange, TransferDirection, TransferItem, TransferSearchChange, TransferSelectChange } from './interface';
import { NzTransferListComponent } from './transfer-list.component';
import * as i0 from "@angular/core";
export declare class NzTransferComponent implements OnInit, OnChanges, OnDestroy {
    private cdr;
    private i18n;
    private elementRef;
    private renderer;
    private directionality;
    private unsubscribe$;
    lists: QueryList<NzTransferListComponent>;
    locale: NzTransferI18nInterface;
    leftFilter: string;
    rightFilter: string;
    dir: Direction;
    prefixCls: string;
    statusCls: NgClassInterface;
    hasFeedback: boolean;
    nzDisabled: boolean;
    nzDataSource: TransferItem[];
    nzTitles: string[];
    nzOperations: string[];
    nzListStyle: NgStyleInterface;
    nzShowSelectAll: boolean;
    nzItemUnit?: string;
    nzItemsUnit?: string;
    nzCanMove: (arg: TransferCanMove) => Observable<TransferItem[]>;
    nzRenderList: Array<TemplateRef<NzSafeAny> | null> | null;
    nzRender: TemplateRef<NzSafeAny> | null;
    nzFooter: TemplateRef<NzSafeAny> | null;
    nzShowSearch: boolean;
    nzFilterOption?: (inputValue: string, item: TransferItem) => boolean;
    nzSearchPlaceholder?: string;
    nzNotFoundContent?: string;
    nzTargetKeys: string[];
    nzSelectedKeys: string[];
    nzStatus: NzStatus;
    nzOneWay: boolean;
    readonly nzChange: EventEmitter<TransferChange>;
    readonly nzSearchChange: EventEmitter<TransferSearchChange>;
    readonly nzSelectChange: EventEmitter<TransferSelectChange>;
    leftDataSource: TransferItem[];
    lastLeftCheckedIndex?: number;
    rightDataSource: TransferItem[];
    lastRightCheckedIndex?: number;
    isShiftPressed: boolean;
    onTriggerShiftDown(): void;
    onTriggerShiftUp(): void;
    onTriggerMouseDown(event: MouseEvent): void;
    private splitDataSource;
    private getCheckedData;
    handleLeftSelectAll: (checked: boolean) => void;
    handleRightSelectAll: (checked: boolean) => void;
    handleLeftSelect: (item: TransferItem) => void;
    handleRightSelect: (item: TransferItem) => void;
    handleSelect(direction: TransferDirection, checked: boolean, item?: TransferItem): void;
    handleFilterChange(ret: {
        direction: TransferDirection;
        value: string;
    }): void;
    leftActive: boolean;
    rightActive: boolean;
    private updateOperationStatus;
    moveToLeft: () => void;
    moveToRight: () => void;
    moveTo(direction: TransferDirection): void;
    private truthMoveTo;
    private nzFormStatusService;
    private nzFormNoStatusService;
    constructor(cdr: ChangeDetectorRef, i18n: NzI18nService, elementRef: ElementRef<HTMLElement>, renderer: Renderer2, directionality: Directionality);
    private markForCheckAllList;
    private handleNzTargetKeys;
    private handleNzSelectedKeys;
    ngOnInit(): void;
    ngOnChanges(changes: SimpleChanges): void;
    ngOnDestroy(): void;
    private setStatusStyles;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTransferComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTransferComponent, "nz-transfer", ["nzTransfer"], { "nzDisabled": { "alias": "nzDisabled"; "required": false; }; "nzDataSource": { "alias": "nzDataSource"; "required": false; }; "nzTitles": { "alias": "nzTitles"; "required": false; }; "nzOperations": { "alias": "nzOperations"; "required": false; }; "nzListStyle": { "alias": "nzListStyle"; "required": false; }; "nzShowSelectAll": { "alias": "nzShowSelectAll"; "required": false; }; "nzItemUnit": { "alias": "nzItemUnit"; "required": false; }; "nzItemsUnit": { "alias": "nzItemsUnit"; "required": false; }; "nzCanMove": { "alias": "nzCanMove"; "required": false; }; "nzRenderList": { "alias": "nzRenderList"; "required": false; }; "nzRender": { "alias": "nzRender"; "required": false; }; "nzFooter": { "alias": "nzFooter"; "required": false; }; "nzShowSearch": { "alias": "nzShowSearch"; "required": false; }; "nzFilterOption": { "alias": "nzFilterOption"; "required": false; }; "nzSearchPlaceholder": { "alias": "nzSearchPlaceholder"; "required": false; }; "nzNotFoundContent": { "alias": "nzNotFoundContent"; "required": false; }; "nzTargetKeys": { "alias": "nzTargetKeys"; "required": false; }; "nzSelectedKeys": { "alias": "nzSelectedKeys"; "required": false; }; "nzStatus": { "alias": "nzStatus"; "required": false; }; "nzOneWay": { "alias": "nzOneWay"; "required": false; }; }, { "nzChange": "nzChange"; "nzSearchChange": "nzSearchChange"; "nzSelectChange": "nzSelectChange"; }, never, never, true, never>;
    static ngAcceptInputType_nzDisabled: unknown;
    static ngAcceptInputType_nzShowSelectAll: unknown;
    static ngAcceptInputType_nzShowSearch: unknown;
    static ngAcceptInputType_nzOneWay: unknown;
}
