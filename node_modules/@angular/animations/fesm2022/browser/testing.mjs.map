{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/testing/src/mock_animation_driver.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimationPlayer,\n  AUTO_STYLE,\n  NoopAnimationPlayer,\n  ɵStyleDataMap,\n} from '../../../src/animations';\nimport {\n  AnimationDriver,\n  ɵallowPreviousPlayerStylesMerge as allowPreviousPlayerStylesMerge,\n  ɵcamelCaseToDashCase,\n  ɵcontainsElement as containsElement,\n  ɵgetParentElement as getParentElement,\n  ɵinvokeQuery as invokeQuery,\n  ɵnormalizeKeyframes as normalizeKeyframes,\n  ɵvalidateStyleProperty as validateStyleProperty,\n  ɵvalidateWebAnimatableStyleProperty,\n} from '../../../browser';\n\n/**\n * @publicApi\n */\nexport class MockAnimationDriver implements AnimationDriver {\n  static log: AnimationPlayer[] = [];\n\n  validateStyleProperty(prop: string): boolean {\n    return validateStyleProperty(prop);\n  }\n\n  validateAnimatableStyleProperty(prop: string): boolean {\n    const cssProp = ɵcamelCaseToDashCase(prop);\n    return ɵvalidateWebAnimatableStyleProperty(cssProp);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean {\n    return containsElement(elm1, elm2);\n  }\n\n  getParentElement(element: unknown): unknown {\n    return getParentElement(element);\n  }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n    element: any,\n    keyframes: Array<ɵStyleDataMap>,\n    duration: number,\n    delay: number,\n    easing: string,\n    previousPlayers: any[] = [],\n  ): MockAnimationPlayer {\n    const player = new MockAnimationPlayer(\n      element,\n      keyframes,\n      duration,\n      delay,\n      easing,\n      previousPlayers,\n    );\n    MockAnimationDriver.log.push(<AnimationPlayer>player);\n    return player;\n  }\n}\n\n/**\n * @publicApi\n */\nexport class MockAnimationPlayer extends NoopAnimationPlayer {\n  private __finished = false;\n  private __started = false;\n  public previousStyles: ɵStyleDataMap = new Map();\n  private _onInitFns: (() => any)[] = [];\n  public currentSnapshot: ɵStyleDataMap = new Map();\n  private _keyframes: Array<ɵStyleDataMap> = [];\n\n  constructor(\n    public element: any,\n    public keyframes: Array<ɵStyleDataMap>,\n    public duration: number,\n    public delay: number,\n    public easing: string,\n    public previousPlayers: any[],\n  ) {\n    super(duration, delay);\n\n    this._keyframes = normalizeKeyframes(keyframes);\n\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousPlayers.forEach((player) => {\n        if (player instanceof MockAnimationPlayer) {\n          const styles = player.currentSnapshot;\n          styles.forEach((val, prop) => this.previousStyles.set(prop, val));\n        }\n      });\n    }\n  }\n\n  /** @internal */\n  onInit(fn: () => any) {\n    this._onInitFns.push(fn);\n  }\n\n  /** @internal */\n  override init() {\n    super.init();\n    this._onInitFns.forEach((fn) => fn());\n    this._onInitFns = [];\n  }\n\n  override reset() {\n    super.reset();\n    this.__started = false;\n  }\n\n  override finish(): void {\n    super.finish();\n    this.__finished = true;\n  }\n\n  override destroy(): void {\n    super.destroy();\n    this.__finished = true;\n  }\n\n  /** @internal */\n  triggerMicrotask() {}\n\n  override play(): void {\n    super.play();\n    this.__started = true;\n  }\n\n  override hasStarted() {\n    return this.__started;\n  }\n\n  beforeDestroy() {\n    const captures: ɵStyleDataMap = new Map();\n\n    this.previousStyles.forEach((val, prop) => captures.set(prop, val));\n\n    if (this.hasStarted()) {\n      // when assembling the captured styles, it's important that\n      // we build the keyframe styles in the following order:\n      // {other styles within keyframes, ... previousStyles }\n      this._keyframes.forEach((kf) => {\n        for (let [prop, val] of kf) {\n          if (prop !== 'offset') {\n            captures.set(prop, this.__finished ? val : AUTO_STYLE);\n          }\n        }\n      });\n    }\n\n    this.currentSnapshot = captures;\n  }\n}\n"], "names": ["ɵcamelCaseToDashCase", "ɵvalidateWebAnimatableStyleProperty"], "mappings": ";;;;;;;;;;AAyBA;;AAEG;MACU,mBAAmB,CAAA;AAC9B,IAAA,OAAO,GAAG,GAAsB,EAAE;AAElC,IAAA,qBAAqB,CAAC,IAAY,EAAA;AAChC,QAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC;;AAGpC,IAAA,+BAA+B,CAAC,IAAY,EAAA;AAC1C,QAAA,MAAM,OAAO,GAAGA,mBAAoB,CAAC,IAAI,CAAC;AAC1C,QAAA,OAAOC,kCAAmC,CAAC,OAAO,CAAC;;IAGrD,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;AAClC,QAAA,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC;;AAGpC,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AAC/B,QAAA,OAAO,gBAAgB,CAAC,OAAO,CAAC;;AAGlC,IAAA,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;QAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAG9C,IAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAA;QAC5D,OAAO,YAAY,IAAI,EAAE;;AAG3B,IAAA,OAAO,CACL,OAAY,EACZ,SAA+B,EAC/B,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAA,GAAyB,EAAE,EAAA;AAE3B,QAAA,MAAM,MAAM,GAAG,IAAI,mBAAmB,CACpC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,KAAK,EACL,MAAM,EACN,eAAe,CAChB;AACD,QAAA,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAkB,MAAM,CAAC;AACrD,QAAA,OAAO,MAAM;;;AAIjB;;AAEG;AACG,MAAO,mBAAoB,SAAQ,mBAAmB,CAAA;AASjD,IAAA,OAAA;AACA,IAAA,SAAA;AACA,IAAA,QAAA;AACA,IAAA,KAAA;AACA,IAAA,MAAA;AACA,IAAA,eAAA;IAbD,UAAU,GAAG,KAAK;IAClB,SAAS,GAAG,KAAK;AAClB,IAAA,cAAc,GAAkB,IAAI,GAAG,EAAE;IACxC,UAAU,GAAkB,EAAE;AAC/B,IAAA,eAAe,GAAkB,IAAI,GAAG,EAAE;IACzC,UAAU,GAAyB,EAAE;IAE7C,WACS,CAAA,OAAY,EACZ,SAA+B,EAC/B,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAsB,EAAA;AAE7B,QAAA,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC;QAPf,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAe,CAAA,eAAA,GAAf,eAAe;AAItB,QAAA,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAC,SAAS,CAAC;AAE/C,QAAA,IAAI,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;AACnD,YAAA,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACjC,gBAAA,IAAI,MAAM,YAAY,mBAAmB,EAAE;AACzC,oBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe;oBACrC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;AAErE,aAAC,CAAC;;;;AAKN,IAAA,MAAM,CAAC,EAAa,EAAA;AAClB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;;;IAIjB,IAAI,GAAA;QACX,KAAK,CAAC,IAAI,EAAE;AACZ,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACrC,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;IAGb,KAAK,GAAA;QACZ,KAAK,CAAC,KAAK,EAAE;AACb,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;IAGf,MAAM,GAAA;QACb,KAAK,CAAC,MAAM,EAAE;AACd,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;IAGf,OAAO,GAAA;QACd,KAAK,CAAC,OAAO,EAAE;AACf,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;;AAIxB,IAAA,gBAAgB;IAEP,IAAI,GAAA;QACX,KAAK,CAAC,IAAI,EAAE;AACZ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;IAGd,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS;;IAGvB,aAAa,GAAA;AACX,QAAA,MAAM,QAAQ,GAAkB,IAAI,GAAG,EAAE;QAEzC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAEnE,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;YAIrB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;gBAC7B,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;AAC1B,oBAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;AACrB,wBAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,UAAU,CAAC;;;AAG5D,aAAC,CAAC;;AAGJ,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ;;AAElC;;;;"}