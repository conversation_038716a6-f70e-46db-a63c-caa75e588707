{"version": 3, "file": "jgo.js", "sourceRoot": "", "sources": ["jgo.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,aAAa,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,EAAC,cAAc,EAAC,iBAAiB,EAAC,aAAa,EAAC,mBAAmB,EAAC,cAAc,EAAC,iBAAiB,EAAC,mBAAmB,EAAC,cAAc,EAAC,iBAAiB,EAAC,gBAAgB,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,6CAA6C,EAAC,6CAA6C,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,iBAAiB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"jgo\",[[\"mbaꞌmbaꞌ\",\"ŋka mbɔ́t nji\"],u,u],u,[[\"Sɔ<PERSON>\",\"Mɔ́\",\"Á<PERSON>\",\"Wɛ́\",\"Tɔ́\",\"Fɛ\",\"Sá\"],[\"Sɔ́ndi\",\"<PERSON>ɔ́<PERSON>\",\"Á<PERSON> Mɔ́ndi\",\"Wɛ́nɛsɛdɛ\",\"Tɔ́sɛdɛ\",\"Fɛlâyɛdɛ\",\"Sásidɛ\"],u,u],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>́<PERSON>á<PERSON>\",\"<PERSON>ɛsa<PERSON>ɛ́nɛ́kwa\",\"Pɛsaŋ <PERSON>aa\",\"Pɛsaŋ Pɛ́nɛ́ntúkú\",\"Pɛsaŋ Saambá\",\"Pɛsaŋ Pɛ́nɛ́fɔm\",\"Pɛsaŋ Pɛ́nɛ́pfúꞋú\",\"Pɛsaŋ Nɛgɛ́m\",\"Pɛsaŋ Ntsɔ̌pmɔ́\",\"Pɛsaŋ Ntsɔ̌ppá\"],u],u,[[\"BCE\",\"CE\"],u,[\"tsɛttsɛt mɛŋguꞌ mi ɛ́ lɛɛnɛ Kɛlísɛtɔ gɔ ńɔ́\",\"tsɛttsɛt mɛŋguꞌ mi ɛ́ fúnɛ Kɛlísɛtɔ tɔ́ mɔ́\"]],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"EEEE, y MMMM dd\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"XAF\",\"FCFA\",\"Fɛlâŋ\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}