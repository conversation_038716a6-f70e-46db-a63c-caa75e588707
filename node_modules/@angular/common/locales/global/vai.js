/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['vai'] = ["vai",[["AM","PM"],u,u],u,[["S","M","T","W","T","F","S"],["ꕞꕌꔵ","ꗳꗡꘉ","ꕚꕞꕚ","ꕉꕞꕒ","ꕉꔤꕆꕢ","ꕉꔤꕀꕮ","ꔻꔬꔳ"],u,u],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["ꖨꖕꔞ","ꕒꕡ","ꕾꖺ","ꖢꖕ","ꖑꕱ","ꖱꘋ","ꖱꕞ","ꗛꔕ","ꕢꕌ","ꕭꖃ","ꔞꘋ","ꖨꖕꗏ"],["ꖨꖕ ꕪꕴ ꔞꔀꕮꕊ","ꕒꕡꖝꖕ","ꕾꖺ","ꖢꖕ","ꖑꕱ","ꖱꘋ","ꖱꕞꔤ","ꗛꔕ","ꕢꕌ","ꕭꖃ","ꔞꘋꕔꕿ ꕸꖃꗏ","ꖨꖕ ꕪꕴ ꗏꖺꕮꕊ"]],u,[["BCE","CE"],u,u],1,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"LRD","$","ꕞꔤꔫꕩ ꕜꕞꕌ",{"JPY":["JP¥","¥"],"LRD":["$"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    