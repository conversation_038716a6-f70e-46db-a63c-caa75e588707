/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['sr-latn-xk'] = ["sr-Latn-XK",[["AM","PM"],u,u],[["a","p"],["AM","PM"],u],[["n","p","u","s","č","p","s"],["ned","pon","uto","sre","čet","pet","sub"],["nedelja","ponedeljak","utorak","sreda","četvrtak","petak","subota"],["ne","po","ut","sr","če","pe","su"]],u,[["j","f","m","a","m","j","j","a","s","o","n","d"],["jan","feb","mart","apr","maj","jun","jul","avg","sept","okt","nov","dec"],["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"]],u,[["p.n.e.","n.e."],["p. n. e.","n. e."],["pre nove ere","nove ere"]],1,[6,0],["d.M.yy.","d. M. y.","d. MMMM y.","EEEE, d. MMMM y."],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"EUR","€","Evro",{"AUD":[u,"$"],"BAM":["KM"],"BYN":[u,"r."],"GEL":[u,"ლ"],"KRW":[u,"₩"],"NZD":[u,"$"],"PHP":[u,"₱"],"TWD":["NT$"],"USD":["US$","$"],"VND":[u,"₫"]},"ltr", plural, [[["ponoć","podne","jutro","po pod.","veče","noć"],["ponoć","podne","jutro","po pod.","uveče","noću"],["ponoć","podne","ujutro","po podne","uveče","noću"]],[["ponoć","podne","jutro","popodne","veče","noć"],u,u],["00:00","12:00",["06:00","12:00"],["12:00","18:00"],["18:00","21:00"],["21:00","06:00"]]]];
  })(globalThis);
    