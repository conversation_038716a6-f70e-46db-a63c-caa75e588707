{"version": 3, "file": "kw.js", "sourceRoot": "", "sources": ["kw.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,KAAK,KAAK,IAAI,CAAC,GAAG,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,KAAK,MAAM,CAAC;QAC7T,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3F,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3G,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,WAAW,EAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,WAAW,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,YAAY,EAAC,aAAa,EAAC,YAAY,EAAC,WAAW,EAAC,QAAQ,EAAC,cAAc,EAAC,eAAe,EAAC,SAAS,EAAC,eAAe,EAAC,WAAW,EAAC,QAAQ,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 0)\n    return 0;\nif (n === 1)\n    return 1;\nif (n % 100 === 2 || (n % 100 === 22 || (n % 100 === 42 || (n % 100 === 62 || n % 100 === 82))) || (n % 1000 === 0 && (n % 100000 === Math.floor(n % 100000) && (n % 100000 >= 1000 && n % 100000 <= 20000 || (n % 100000 === 40000 || (n % 100000 === 60000 || n % 100000 === 80000)))) || !(n === 0) && n % 1000000 === 100000))\n    return 2;\nif (n % 100 === 3 || (n % 100 === 23 || (n % 100 === 43 || (n % 100 === 63 || n % 100 === 83))))\n    return 3;\nif (!(n === 1) && (n % 100 === 1 || (n % 100 === 21 || (n % 100 === 41 || (n % 100 === 61 || n % 100 === 81)))))\n    return 4;\nreturn 5;\n}\n\nexport default [\"kw\",[[\"a.m.\",\"p.m.\"],u,u],u,[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"Sul\",\"Lun\",\"Mth\",\"Mhr\",\"Yow\",\"Gwe\",\"Sad\"],[\"dy Sul\",\"dy Lun\",\"dy Meurth\",\"dy Merher\",\"dy Yow\",\"dy Gwener\",\"dy Sadorn\"],[\"Sul\",\"Lun\",\"Mth\",\"Mhr\",\"Yow\",\"Gwe\",\"Sad\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"Gen\",\"Hwe\",\"Meu\",\"Ebr\",\"Me\",\"Met\",\"Gor\",\"Est\",\"Gwn\",\"Hed\",\"Du\",\"Kev\"],[\"mis Genver\",\"mis Hwevrer\",\"mis Meurth\",\"mis Ebrel\",\"mis Me\",\"mis Metheven\",\"mis Gortheren\",\"mis Est\",\"mis Gwynngala\",\"mis Hedra\",\"mis Du\",\"mis Kevardhu\"]],u,[[\"RC\",\"AD\"],u,u],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"GBP\",\"£\",\"GBP\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}