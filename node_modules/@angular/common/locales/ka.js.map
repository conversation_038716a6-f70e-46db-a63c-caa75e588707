{"version": 3, "file": "ka.js", "sourceRoot": "", "sources": ["ka.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,WAAW,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,YAAY,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,qBAAqB,EAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,kBAAkB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,gBAAgB,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ka\",[[\"a\",\"p\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"კ\",\"ო\",\"ს\",\"ო\",\"ხ\",\"პ\",\"შ\"],[\"კვი\",\"ორშ\",\"სამ\",\"ოთხ\",\"ხუთ\",\"პარ\",\"შაბ\"],[\"კვირა\",\"ორშაბათი\",\"სამშაბათი\",\"ოთხშაბათი\",\"ხუთშაბათი\",\"პარასკევი\",\"შაბათი\"],[\"კვ\",\"ორ\",\"სმ\",\"ოთ\",\"ხთ\",\"პრ\",\"შბ\"]],u,[[\"ი\",\"თ\",\"მ\",\"ა\",\"მ\",\"ი\",\"ი\",\"ა\",\"ს\",\"ო\",\"ნ\",\"დ\"],[\"იან\",\"თებ\",\"მარ\",\"აპრ\",\"მაი\",\"ივნ\",\"ივლ\",\"აგვ\",\"სექ\",\"ოქტ\",\"ნოე\",\"დეკ\"],[\"იანვარი\",\"თებერვალი\",\"მარტი\",\"აპრილი\",\"მაისი\",\"ივნისი\",\"ივლისი\",\"აგვისტო\",\"სექტემბერი\",\"ოქტომბერი\",\"ნოემბერი\",\"დეკემბერი\"]],u,[[\"ძვ. წ.\",\"ახ. წ.\"],u,[\"ძველი წელთაღრიცხვით\",\"ახალი წელთაღრიცხვით\"]],1,[6,0],[\"dd.MM.yy\",\"d MMM. y\",\"d MMMM, y\",\"EEEE, dd MMMM, y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"არ არის რიცხვი\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"GEL\",\"₾\",\"ქართული ლარი\",{\"AUD\":[u,\"$\"],\"BYN\":[u,\"р.\"],\"CNY\":[u,\"¥\"],\"GEL\":[\"₾\"],\"HKD\":[u,\"$\"],\"ILS\":[u,\"₪\"],\"INR\":[u,\"₹\"],\"JPY\":[u,\"¥\"],\"KRW\":[u,\"₩\"],\"NZD\":[u,\"$\"],\"PHP\":[u,\"₱\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"VND\":[u,\"₫\"]},\"ltr\", plural];\n"]}