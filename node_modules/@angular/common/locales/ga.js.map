{"version": 3, "file": "ga.js", "sourceRoot": "", "sources": ["ga.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1C,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,cAAc,EAAC,UAAU,EAAC,UAAU,EAAC,aAAa,EAAC,WAAW,EAAC,WAAW,EAAC,aAAa,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,SAAS,EAAC,WAAW,EAAC,WAAW,EAAC,MAAM,EAAC,QAAQ,EAAC,cAAc,EAAC,kBAAkB,EAAC,SAAS,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,eAAe,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,OAAO,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nif (n === 2)\n    return 2;\nif (n === Math.floor(n) && (n >= 3 && n <= 6))\n    return 3;\nif (n === Math.floor(n) && (n >= 7 && n <= 10))\n    return 4;\nreturn 5;\n}\n\nexport default [\"ga\",[[\"r.n.\",\"i.n.\"],u,u],u,[[\"D\",\"L\",\"M\",\"C\",\"D\",\"A\",\"S\"],[\"<PERSON>h\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>th\"],[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>oine\",\"<PERSON><PERSON>\"],[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"A\",\"B\",\"M\",\"I\",\"<PERSON>\",\"M\",\"D\",\"S\",\"N\"],[\"<PERSON>an\",\"<PERSON>abh\",\"M<PERSON>rta\",\"<PERSON>b\",\"<PERSON>al\",\"<PERSON>th\",\"I<PERSON>il\",\"L<PERSON>\",\"M<PERSON><PERSON>mh\",\"<PERSON><PERSON><PERSON>mh\",\"<PERSON>h\",\"<PERSON>ll\"],[\"Eanáir\",\"Feabhra\",\"Márta\",\"Aibreán\",\"Bealtaine\",\"Meitheamh\",\"Iúil\",\"Lúnasa\",\"Meán Fómhair\",\"Deireadh Fómhair\",\"Samhain\",\"Nollaig\"]],u,[[\"RC\",\"AD\"],u,[\"Roimh Chríost\",\"Anno Domini\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"Nuimh\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"EUR\",\"€\",\"Euro\",{\"RUR\":[u,\"р.\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"XXX\":[]},\"ltr\", plural];\n"]}