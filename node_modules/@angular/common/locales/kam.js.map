{"version": 3, "file": "kam.js", "sourceRoot": "", "sources": ["kam.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,gBAAgB,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,WAAW,EAAC,cAAc,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,cAAc,EAAC,cAAc,EAAC,gBAAgB,EAAC,cAAc,EAAC,gBAAgB,EAAC,mBAAmB,EAAC,gBAAgB,EAAC,iBAAiB,EAAC,eAAe,EAAC,eAAe,EAAC,uBAAuB,EAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,kBAAkB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"kam\",[[\"Ĩyakwakya\",\"Ĩyawĩoo\"],u,u],u,[[\"Y\",\"W\",\"E\",\"A\",\"A\",\"A\",\"A\"],[\"Wky\",\"Wkw\",\"Wkl\",\"Wtũ\",\"Wkn\",\"Wtn\",\"Wth\"],[\"Wa kyumwa\",\"Wa kwambĩlĩlya\",\"Wa kelĩ\",\"Wa katatũ\",\"Wa kana\",\"Wa katano\",\"Wa thanthatũ\"],[\"Wky\",\"Wkw\",\"Wkl\",\"Wtũ\",\"Wkn\",\"Wtn\",\"Wth\"]],u,[[\"M\",\"K\",\"K\",\"K\",\"K\",\"T\",\"M\",\"N\",\"K\",\"Ĩ\",\"Ĩ\",\"Ĩ\"],[\"Mbe\",\"Kel\",\"<PERSON>t<PERSON>\",\"Kan\",\"Ktn\",\"Tha\",\"<PERSON>o\",\"Nya\",\"Knd\",\"Ĩku\",\"Ĩkm\",\"Ĩkl\"],[\"Mwai wa mbee\",\"Mwai wa kelĩ\",\"Mwai wa katatũ\",\"Mwai wa kana\",\"Mwai wa katano\",\"Mwai wa thanthatũ\",\"Mwai wa muonza\",\"Mwai wa nyaanya\",\"Mwai wa kenda\",\"Mwai wa ĩkumi\",\"Mwai wa ĩkumi na ĩmwe\",\"Mwai wa ĩkumi na ilĩ\"]],u,[[\"MY\",\"IY\"],u,[\"Mbee wa Yesũ\",\"Ĩtina wa Yesũ\"]],0,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"KES\",\"Ksh\",\"Silingi ya Kenya\",{\"JPY\":[\"JP¥\",\"¥\"],\"KES\":[\"Ksh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}