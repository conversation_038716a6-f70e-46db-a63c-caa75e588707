{"version": 3, "file": "km.js", "sourceRoot": "", "sources": ["km.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,iBAAiB,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"km\",[[\"a\",\"p\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"អ\",\"ច\",\"អ\",\"ព\",\"ព\",\"ស\",\"ស\"],[\"អាទិត្យ\",\"ចន្ទ\",\"អង្គារ\",\"ពុធ\",\"ព្រហ\",\"សុក្រ\",\"សៅរ៍\"],[\"អាទិត្យ\",\"ច័ន្ទ\",\"អង្គារ\",\"ពុធ\",\"ព្រហស្បតិ៍\",\"សុក្រ\",\"សៅរ៍\"],[\"អា\",\"ច\",\"អ\",\"ពុ\",\"ព្រ\",\"សុ\",\"ស\"]],[[\"អ\",\"ច\",\"អ\",\"ព\",\"ព\",\"ស\",\"ស\"],[\"អាទិត្យ\",\"ចន្ទ\",\"អង្គារ\",\"ពុធ\",\"ព្រហ\",\"សុក្រ\",\"សៅរ៍\"],[\"អាទិត្យ\",\"ចន្ទ\",\"អង្គារ\",\"ពុធ\",\"ព្រហស្បតិ៍\",\"សុក្រ\",\"សៅរ៍\"],[\"អា\",\"ច\",\"អ\",\"ពុ\",\"ព្រ\",\"សុ\",\"ស\"]],[[\"ម\",\"ក\",\"ម\",\"ម\",\"ឧ\",\"ម\",\"ក\",\"ស\",\"ក\",\"ត\",\"វ\",\"ធ\"],[\"មករា\",\"កុម្ភៈ\",\"មីនា\",\"មេសា\",\"ឧសភា\",\"មិថុនា\",\"កក្កដា\",\"សីហា\",\"កញ្ញា\",\"តុលា\",\"វិច្ឆិកា\",\"ធ្នូ\"],u],u,[[\"មុន គ.ស.\",\"គ.ស.\"],u,[\"មុន​គ្រិស្តសករាជ\",\"គ្រិស្តសករាជ\"]],0,[6,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} នៅ​ម៉ោង {0}\",u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00¤\",\"#E0\"],\"KHR\",\"៛\",\"រៀល​កម្ពុជា\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"KHR\":[\"៛\"],\"LSL\":[\"ឡូទី\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}