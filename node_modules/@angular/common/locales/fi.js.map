{"version": 3, "file": "fi.js", "sourceRoot": "", "sources": ["fi.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAEjG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,<PERSON><PERSON>,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,EAAC,WAAW,EAAC,eAAe,EAAC,WAAW,EAAC,aAAa,EAAC,YAAY,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,SAAS,EAAC,aAAa,EAAC,SAAS,EAAC,WAAW,EAAC,UAAU,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,YAAY,EAAC,YAAY,EAAC,aAAa,EAAC,YAAY,EAAC,YAAY,EAAC,WAAW,EAAC,YAAY,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,EAAC,aAAa,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,2BAA2B,EAAC,6BAA6B,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,CAAC,EAAC,WAAW,EAAC,gBAAgB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,cAAc,CAAC,EAAC,CAAC,SAAS,EAAC,eAAe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sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n\nif (i === 1 && v === 0)\n    return 1;\nreturn 5;\n}\n\nexport default [\"fi\",[[\"ap.\",\"ip.\"],u,u],u,[[\"S\",\"M\",\"T\",\"K\",\"T\",\"P\",\"L\"],[\"su\",\"ma\",\"ti\",\"ke\",\"to\",\"pe\",\"la\"],[\"sunnuntaina\",\"maanantaina\",\"tiistaina\",\"keskiviikkona\",\"torstaina\",\"perjantaina\",\"lauantaina\"],[\"su\",\"ma\",\"ti\",\"ke\",\"to\",\"pe\",\"la\"]],[[\"S\",\"M\",\"T\",\"K\",\"T\",\"P\",\"L\"],[\"su\",\"ma\",\"ti\",\"ke\",\"to\",\"pe\",\"la\"],[\"sunnuntai\",\"maanantai\",\"tiistai\",\"keskiviikko\",\"torstai\",\"perjantai\",\"lauantai\"],[\"su\",\"ma\",\"ti\",\"ke\",\"to\",\"pe\",\"la\"]],[[\"T\",\"H\",\"M\",\"H\",\"T\",\"K\",\"H\",\"E\",\"S\",\"L\",\"M\",\"J\"],[\"tammik.\",\"helmik.\",\"maalisk.\",\"huhtik.\",\"toukok.\",\"kesäk.\",\"heinäk.\",\"elok.\",\"syysk.\",\"lokak.\",\"marrask.\",\"jouluk.\"],[\"tammikuuta\",\"helmikuuta\",\"maaliskuuta\",\"huhtikuuta\",\"toukokuuta\",\"kesäkuuta\",\"heinäkuuta\",\"elokuuta\",\"syyskuuta\",\"lokakuuta\",\"marraskuuta\",\"joulukuuta\"]],[[\"T\",\"H\",\"M\",\"H\",\"T\",\"K\",\"H\",\"E\",\"S\",\"L\",\"M\",\"J\"],[\"tammi\",\"helmi\",\"maalis\",\"huhti\",\"touko\",\"kesä\",\"heinä\",\"elo\",\"syys\",\"loka\",\"marras\",\"joulu\"],[\"tammikuu\",\"helmikuu\",\"maaliskuu\",\"huhtikuu\",\"toukokuu\",\"kesäkuu\",\"heinäkuu\",\"elokuu\",\"syyskuu\",\"lokakuu\",\"marraskuu\",\"joulukuu\"]],[[\"eKr\",\"jKr\"],[\"eKr.\",\"jKr.\"],[\"ennen Kristuksen syntymää\",\"jälkeen Kristuksen syntymän\"]],1,[6,0],[\"d.M.y\",u,\"d. MMMM y\",\"cccc d. MMMM y\"],[\"H.mm\",\"H.mm.ss\",\"H.mm.ss z\",\"H.mm.ss zzzz\"],[\"{1} {0}\",\"{1} 'klo' {0}\",u,u],[\",\",\" \",\";\",\"%\",\"+\",\"−\",\"E\",\"×\",\"‰\",\"∞\",\"epäluku\",\".\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"euro\",{\"AOA\":[],\"ARS\":[],\"AUD\":[],\"BAM\":[],\"BBD\":[],\"BDT\":[],\"BMD\":[],\"BND\":[],\"BOB\":[],\"BRL\":[],\"BSD\":[],\"BWP\":[],\"BZD\":[],\"CAD\":[],\"CLP\":[],\"CNY\":[],\"COP\":[],\"CRC\":[],\"CUC\":[],\"CUP\":[],\"CZK\":[],\"DKK\":[],\"DOP\":[],\"EGP\":[],\"ESP\":[],\"FIM\":[\"mk\"],\"FJD\":[],\"FKP\":[],\"GEL\":[],\"GIP\":[],\"GNF\":[],\"GTQ\":[],\"GYD\":[],\"HKD\":[],\"HNL\":[],\"HRK\":[],\"HUF\":[],\"IDR\":[],\"ILS\":[],\"INR\":[],\"ISK\":[],\"JMD\":[],\"KHR\":[],\"KMF\":[],\"KPW\":[],\"KRW\":[],\"KYD\":[],\"KZT\":[],\"LAK\":[],\"LBP\":[],\"LKR\":[],\"LRD\":[],\"LTL\":[],\"LVL\":[],\"MGA\":[],\"MMK\":[],\"MNT\":[],\"MUR\":[],\"MXN\":[],\"MYR\":[],\"NAD\":[],\"NGN\":[],\"NIO\":[],\"NOK\":[],\"NPR\":[],\"NZD\":[],\"PHP\":[],\"PKR\":[],\"PLN\":[],\"PYG\":[],\"RON\":[],\"RWF\":[],\"SBD\":[],\"SEK\":[],\"SGD\":[],\"SHP\":[],\"SRD\":[],\"SSP\":[],\"STN\":[u,\"STD\"],\"SYP\":[],\"THB\":[],\"TOP\":[],\"TRY\":[],\"TTD\":[],\"TWD\":[],\"UAH\":[],\"UYU\":[],\"VEF\":[],\"VND\":[],\"XCD\":[],\"XPF\":[],\"XXX\":[],\"ZAR\":[],\"ZMW\":[]},\"ltr\", plural];\n"]}