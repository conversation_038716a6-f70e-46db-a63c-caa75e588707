{"version": 3, "file": "lv.js", "sourceRoot": "", "sources": ["lv.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,UAAU,EAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,UAAU,EAAC,cAAc,EAAC,SAAS,EAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,OAAO,EAAC,MAAM,EAAC,UAAU,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,UAAU,EAAC,cAAc,EAAC,MAAM,EAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"pusnaktī\",\"pusd.\",\"no rīta\",\"pēcpusd.\",\"vakarā\",\"naktī\"],u,[\"pusnaktī\",\"pusdienlaikā\",\"no rīta\",\"pēcpusdienā\",\"vakarā\",\"naktī\"]],[[\"pusnakts\",\"pusd.\",\"rīts\",\"pēcpusd.\",\"vakars\",\"nakts\"],u,[\"pusnakts\",\"pusdienlaiks\",\"rīts\",\"pēcpusdiena\",\"vakars\",\"nakts\"]],[\"00:00\",\"12:00\",[\"06:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"23:00\"],[\"23:00\",\"06:00\"]]];\n"]}