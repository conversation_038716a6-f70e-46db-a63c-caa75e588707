{"version": 3, "file": "ff-MR.js", "sourceRoot": "", "sources": ["ff-MR.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,+FAA+F;AAC/F,+FAA+F;AAC/F,4FAA4F;AAC5F,gGAAgG;AAChG,gGAAgG;AAChG,iFAAiF;AAEjF,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,CAAS;IACvB,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,CAAC;IACjC,OAAO,CAAC,CAAC;AACX,CAAC;AAED,eAAe;IACb,OAAO;IACP,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IACD;QACE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QACnC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QACjD,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,CAAC;QAChF,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;KAClD;IACD,CAAC;IACD;QACE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC5D,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QACpF;YACE,OAAO;YACP,OAAO;YACP,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,OAAO;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,OAAO;YACP,OAAO;SACR;KACF;IACD,CAAC;IACD,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IACjD,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC;IACN,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC;IAClD,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC;IACxD,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC;IAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC;IAC5C,IAAI;IACJ,iBAAiB;IACjB,EAAC,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,EAAC;IACzD,MAAM;CACP,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// **Note**: Locale files are generated through Bazel and never part of the sources. This is an\n// exception for backwards compatibility. With the Gulp setup we never deleted old locale files\n// when updating CLDR, so older locale files which have been removed, or renamed in the CLDR\n// data remained in the repository. We keep these files checked-in until the next major to avoid\n// potential breaking changes. It's worth noting that the locale data for such files is outdated\n// anyway. e.g. the data is missing the directionality, throwing off the indices.\n\nconst u = undefined;\n\nfunction plural(n: number): number {\n  let i = Math.floor(Math.abs(n));\n  if (i === 0 || i === 1) return 1;\n  return 5;\n}\n\nexport default [\n  'ff-MR',\n  [['subaka', 'kikiiɗe'], u, u],\n  u,\n  [\n    ['d', 'a', 'm', 'n', 'n', 'm', 'h'],\n    ['dew', 'aaɓ', 'maw', 'nje', 'naa', 'mwd', 'hbi'],\n    ['dewo', 'aaɓnde', 'mawbaare', 'njeslaare', 'naasaande', 'mawnde', 'hoore-biir'],\n    ['dew', 'aaɓ', 'maw', 'nje', 'naa', 'mwd', 'hbi'],\n  ],\n  u,\n  [\n    ['s', 'c', 'm', 's', 'd', 'k', 'm', 'j', 's', 'y', 'j', 'b'],\n    ['sii', 'col', 'mbo', 'see', 'duu', 'kor', 'mor', 'juk', 'slt', 'yar', 'jol', 'bow'],\n    [\n      'siilo',\n      'colte',\n      'mbooy',\n      'seeɗto',\n      'duujal',\n      'korse',\n      'morso',\n      'juko',\n      'siilto',\n      'yarkomaa',\n      'jolal',\n      'bowte',\n    ],\n  ],\n  u,\n  [['H-I', 'C-I'], u, ['Hade Iisa', 'Caggal Iisa']],\n  1,\n  [6, 0],\n  ['d/M/y', 'd MMM, y', 'd MMMM y', 'EEEE d MMMM y'],\n  ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'],\n  ['{1} {0}', u, u, u],\n  [',', ' ', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n  ['#,##0.###', '#,##0%', '#,##0.00 ¤', '#E0'],\n  'UM',\n  'Ugiyya Muritani',\n  {'JPY': ['JP¥', '¥'], 'MRU': ['UM'], 'USD': ['US$', '$']},\n  plural,\n];\n"]}