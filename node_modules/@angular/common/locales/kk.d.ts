/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BYN: (string | undefined)[];
    JPY: string[];
    KZT: string[];
    LSL: string[];
    PHP: (string | undefined)[];
    RUB: string[];
    THB: string[];
    TWD: string[];
} | undefined)[];
export default _default;
