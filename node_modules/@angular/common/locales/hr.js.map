{"version": 3, "file": "hr.js", "sourceRoot": "", "sources": ["hr.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAEjK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;QACnF,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;QACrN,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,OAAO,EAAC,WAAW,EAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,cAAc,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,YAAY,EAAC,WAAW,EAAC,YAAY,EAAC,kBAAkB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,iBAAiB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,aAAa,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n\nif (v === 0 && (i % 10 === 1 && !(i % 100 === 11)) || f % 10 === 1 && !(f % 100 === 11))\n    return 1;\nif (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)) || f % 10 === Math.floor(f % 10) && (f % 10 >= 2 && f % 10 <= 4) && !(f % 100 >= 12 && f % 100 <= 14))\n    return 3;\nreturn 5;\n}\n\nexport default [\"hr\",[[\"AM\",\"PM\"],u,u],u,[[\"N\",\"P\",\"U\",\"S\",\"Č\",\"P\",\"S\"],[\"ned\",\"pon\",\"uto\",\"sri\",\"čet\",\"pet\",\"sub\"],[\"nedjelja\",\"ponedjeljak\",\"utorak\",\"srijeda\",\"četvrtak\",\"petak\",\"subota\"],[\"ned\",\"pon\",\"uto\",\"sri\",\"čet\",\"pet\",\"sub\"]],[[\"n\",\"p\",\"u\",\"s\",\"č\",\"p\",\"s\"],[\"ned\",\"pon\",\"uto\",\"sri\",\"čet\",\"pet\",\"sub\"],[\"nedjelja\",\"ponedjeljak\",\"utorak\",\"srijeda\",\"četvrtak\",\"petak\",\"subota\"],[\"ned\",\"pon\",\"uto\",\"sri\",\"čet\",\"pet\",\"sub\"]],[[\"1.\",\"2.\",\"3.\",\"4.\",\"5.\",\"6.\",\"7.\",\"8.\",\"9.\",\"10.\",\"11.\",\"12.\"],[\"sij\",\"velj\",\"ožu\",\"tra\",\"svi\",\"lip\",\"srp\",\"kol\",\"ruj\",\"lis\",\"stu\",\"pro\"],[\"siječnja\",\"veljače\",\"ožujka\",\"travnja\",\"svibnja\",\"lipnja\",\"srpnja\",\"kolovoza\",\"rujna\",\"listopada\",\"studenoga\",\"prosinca\"]],[[\"1.\",\"2.\",\"3.\",\"4.\",\"5.\",\"6.\",\"7.\",\"8.\",\"9.\",\"10.\",\"11.\",\"12.\"],[\"sij\",\"velj\",\"ožu\",\"tra\",\"svi\",\"lip\",\"srp\",\"kol\",\"ruj\",\"lis\",\"stu\",\"pro\"],[\"siječanj\",\"veljača\",\"ožujak\",\"travanj\",\"svibanj\",\"lipanj\",\"srpanj\",\"kolovoz\",\"rujan\",\"listopad\",\"studeni\",\"prosinac\"]],[[\"pr.n.e.\",\"AD\"],[\"pr. Kr.\",\"po. Kr.\"],[\"prije Krista\",\"poslije Krista\"]],1,[6,0],[\"dd. MM. y.\",\"d. MMM y.\",\"d. MMMM y.\",\"EEEE, d. MMMM y.\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss (zzzz)\"],[\"{1} {0}\",u,\"{1} 'u' {0}\",u],[\",\",\".\",\";\",\"%\",\"+\",\"−\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"HRK\",\"kn\",\"hrvatska kuna\",{\"AUD\":[u,\"$\"],\"BRL\":[u,\"R$\"],\"BYN\":[u,\"р.\"],\"CAD\":[u,\"$\"],\"CNY\":[u,\"¥\"],\"EUR\":[u,\"€\"],\"GBP\":[u,\"£\"],\"HKD\":[u,\"$\"],\"HRK\":[\"kn\"],\"ILS\":[u,\"₪\"],\"INR\":[u,\"₹\"],\"JPY\":[u,\"¥\"],\"KRW\":[u,\"₩\"],\"MXN\":[u,\"$\"],\"NZD\":[u,\"$\"],\"PHP\":[u,\"₱\"],\"RUR\":[u,\"р.\"],\"TWD\":[u,\"NT$\"],\"USD\":[u,\"$\"],\"VND\":[u,\"₫\"],\"XCD\":[u,\"$\"],\"XPF\":[],\"XXX\":[]},\"ltr\", plural];\n"]}