import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-input-area',
  imports: [
    FormsModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule
  ],
  templateUrl: './input-area.component.html',
  styleUrl: './input-area.component.scss'
})
export class InputAreaComponent {
  inputValue = '';

  onSend() {
    if (this.inputValue.trim()) {
      console.log('Sending message:', this.inputValue);
      // Handle send logic here
      this.inputValue = '';
    }
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSend();
    }
  }
}
