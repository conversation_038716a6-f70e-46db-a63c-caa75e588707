<div class="input-container bg-white border border-gray-300 rounded-xl px-4 py-3 flex items-center gap-3 shadow-lg">
  <input
    nz-input
    [(ngModel)]="inputValue"
    (keypress)="onKeyPress($event)"
    placeholder="Попитайте Agrimi AI"
    class="flex-1 border-none outline-none font-montserrat font-medium text-sm text-gray-800 placeholder-gray-400"
    style="box-shadow: none; border: none;"
  />
  
  <button
    nz-button
    nzType="text"
    nzShape="circle"
    (click)="onSend()"
    class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 transition-colors"
    [disabled]="!inputValue.trim()">
    <div class="w-8 h-8 flex items-center justify-center">
      <span class="w-6 h-6 bg-gray-400 rounded"></span>
    </div>
  </button>
</div>
