import { Component } from '@angular/core';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-sidebar',
  imports: [NzLayoutModule, NzButtonModule, NzIconModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
export class SidebarComponent {
  conversations = [
    { id: 1, title: 'Conversation 1', date: 'Today' },
    { id: 2, title: 'Previous Chat', date: 'Yesterday' },
    { id: 3, title: 'Another Chat', date: '2 days ago' },
  ];
}
