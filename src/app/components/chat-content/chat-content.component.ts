import { Component } from '@angular/core';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { InputAreaComponent } from '../input-area/input-area.component';

@Component({
  selector: 'app-chat-content',
  imports: [
    NzLayoutModule,
    NzFlexModule,
    InputAreaComponent
  ],
  templateUrl: './chat-content.component.html',
  styleUrl: './chat-content.component.scss'
})
export class ChatContentComponent {
  suggestedQuestions = [
    'Какво означават цветовете на картата?',
    'Как се сменя статус на договор?',
    'Как да филтрирам имоти по собственик или масив?'
  ];
}
