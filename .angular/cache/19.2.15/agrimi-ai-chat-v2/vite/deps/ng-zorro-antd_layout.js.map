{"version": 3, "sources": ["../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-layout.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ViewEncapsulation, ChangeDetectionStrategy, Component, Input, EventEmitter, booleanAttribute, Output, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2 from 'ng-zorro-antd/core/services';\nimport { siderResponsiveMap } from 'ng-zorro-antd/core/services';\nimport { toCssPixel, inNextTick } from 'ng-zorro-antd/core/util';\nimport { NzMenuDirective } from 'ng-zorro-antd/menu';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i1$1 from '@angular/cdk/platform';\nimport * as i1$2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"nz-sider-trigger\", \"\"];\nfunction NzSiderTriggerComponent_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzSiderTriggerComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSiderTriggerComponent_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const defaultZeroTrigger_r2 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzZeroTrigger || defaultZeroTrigger_r2);\n  }\n}\nfunction NzSiderTriggerComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzSiderTriggerComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSiderTriggerComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const defaultTrigger_r3 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzTrigger || defaultTrigger_r3);\n  }\n}\nfunction NzSiderTriggerComponent_ng_template_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzType\", ctx_r0.nzCollapsed ? \"left\" : \"right\");\n  }\n}\nfunction NzSiderTriggerComponent_ng_template_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzType\", ctx_r0.nzCollapsed ? \"right\" : \"left\");\n  }\n}\nfunction NzSiderTriggerComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSiderTriggerComponent_ng_template_2_Conditional_0_Template, 1, 1, \"nz-icon\", 3)(1, NzSiderTriggerComponent_ng_template_2_Conditional_1_Template, 1, 1, \"nz-icon\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.nzReverseArrow ? 0 : 1);\n  }\n}\nfunction NzSiderTriggerComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 4);\n  }\n}\nfunction NzSiderComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function NzSiderComponent_Conditional_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setCollapsed(!ctx_r1.nzCollapsed));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matchBreakPoint\", ctx_r1.matchBreakPoint)(\"nzCollapsedWidth\", ctx_r1.nzCollapsedWidth)(\"nzCollapsed\", ctx_r1.nzCollapsed)(\"nzBreakpoint\", ctx_r1.nzBreakpoint)(\"nzReverseArrow\", ctx_r1.nzReverseArrow)(\"nzTrigger\", ctx_r1.nzTrigger)(\"nzZeroTrigger\", ctx_r1.nzZeroTrigger)(\"siderWidth\", ctx_r1.widthSetting);\n  }\n}\nclass NzContentComponent {\n  elementRef;\n  renderer;\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.renderer.addClass(this.elementRef.nativeElement, 'ant-layout-content');\n  }\n  static ɵfac = function NzContentComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzContentComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzContentComponent,\n    selectors: [[\"nz-content\"]],\n    exportAs: [\"nzContent\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzContentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzContentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-content',\n      exportAs: 'nzContent',\n      template: `<ng-content></ng-content>`,\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFooterComponent {\n  elementRef;\n  renderer;\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.renderer.addClass(this.elementRef.nativeElement, 'ant-layout-footer');\n  }\n  static ɵfac = function NzFooterComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFooterComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFooterComponent,\n    selectors: [[\"nz-footer\"]],\n    exportAs: [\"nzFooter\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-footer',\n      exportAs: 'nzFooter',\n      template: `<ng-content></ng-content>`,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzHeaderComponent {\n  elementRef;\n  renderer;\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.renderer.addClass(this.elementRef.nativeElement, 'ant-layout-header');\n  }\n  static ɵfac = function NzHeaderComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzHeaderComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzHeaderComponent,\n    selectors: [[\"nz-header\"]],\n    exportAs: [\"nzHeader\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzHeaderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-header',\n      exportAs: 'nzHeader',\n      template: `<ng-content></ng-content>`,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSiderTriggerComponent {\n  nzCollapsed = false;\n  nzReverseArrow = false;\n  nzZeroTrigger = null;\n  nzTrigger = undefined;\n  matchBreakPoint = false;\n  nzCollapsedWidth = null;\n  siderWidth = null;\n  nzBreakpoint = null;\n  isZeroTrigger = false;\n  isNormalTrigger = false;\n  updateTriggerType() {\n    this.isZeroTrigger = this.nzCollapsedWidth === 0 && (this.nzBreakpoint && this.matchBreakPoint || !this.nzBreakpoint);\n    this.isNormalTrigger = this.nzCollapsedWidth !== 0;\n  }\n  ngOnInit() {\n    this.updateTriggerType();\n  }\n  ngOnChanges() {\n    this.updateTriggerType();\n  }\n  static ɵfac = function NzSiderTriggerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSiderTriggerComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzSiderTriggerComponent,\n    selectors: [[\"\", \"nz-sider-trigger\", \"\"]],\n    hostVars: 10,\n    hostBindings: function NzSiderTriggerComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"width\", ctx.isNormalTrigger ? ctx.siderWidth : null);\n        i0.ɵɵclassProp(\"ant-layout-sider-trigger\", ctx.isNormalTrigger)(\"ant-layout-sider-zero-width-trigger\", ctx.isZeroTrigger)(\"ant-layout-sider-zero-width-trigger-right\", ctx.isZeroTrigger && ctx.nzReverseArrow)(\"ant-layout-sider-zero-width-trigger-left\", ctx.isZeroTrigger && !ctx.nzReverseArrow);\n      }\n    },\n    inputs: {\n      nzCollapsed: \"nzCollapsed\",\n      nzReverseArrow: \"nzReverseArrow\",\n      nzZeroTrigger: \"nzZeroTrigger\",\n      nzTrigger: \"nzTrigger\",\n      matchBreakPoint: \"matchBreakPoint\",\n      nzCollapsedWidth: \"nzCollapsedWidth\",\n      siderWidth: \"siderWidth\",\n      nzBreakpoint: \"nzBreakpoint\"\n    },\n    exportAs: [\"nzSiderTrigger\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c1,\n    decls: 6,\n    vars: 2,\n    consts: [[\"defaultTrigger\", \"\"], [\"defaultZeroTrigger\", \"\"], [3, \"ngTemplateOutlet\"], [3, \"nzType\"], [\"nzType\", \"bars\"]],\n    template: function NzSiderTriggerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzSiderTriggerComponent_Conditional_0_Template, 1, 1, null, 2)(1, NzSiderTriggerComponent_Conditional_1_Template, 1, 1, null, 2)(2, NzSiderTriggerComponent_ng_template_2_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzSiderTriggerComponent_ng_template_4_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.isZeroTrigger ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.isNormalTrigger ? 1 : -1);\n      }\n    },\n    dependencies: [NgTemplateOutlet, NzIconModule, i1.NzIconDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSiderTriggerComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-sider-trigger]',\n      exportAs: 'nzSiderTrigger',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (isZeroTrigger) {\n      <ng-template [ngTemplateOutlet]=\"nzZeroTrigger || defaultZeroTrigger\" />\n    }\n\n    @if (isNormalTrigger) {\n      <ng-template [ngTemplateOutlet]=\"nzTrigger || defaultTrigger\" />\n    }\n    <ng-template #defaultTrigger>\n      @if (nzReverseArrow) {\n        <nz-icon [nzType]=\"nzCollapsed ? 'left' : 'right'\" />\n      } @else {\n        <nz-icon [nzType]=\"nzCollapsed ? 'right' : 'left'\" />\n      }\n    </ng-template>\n    <ng-template #defaultZeroTrigger>\n      <nz-icon nzType=\"bars\" />\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-layout-sider-trigger]': 'isNormalTrigger',\n        '[style.width]': 'isNormalTrigger ? siderWidth : null',\n        '[class.ant-layout-sider-zero-width-trigger]': 'isZeroTrigger',\n        '[class.ant-layout-sider-zero-width-trigger-right]': 'isZeroTrigger && nzReverseArrow',\n        '[class.ant-layout-sider-zero-width-trigger-left]': 'isZeroTrigger && !nzReverseArrow'\n      },\n      imports: [NgTemplateOutlet, NzIconModule]\n    }]\n  }], null, {\n    nzCollapsed: [{\n      type: Input\n    }],\n    nzReverseArrow: [{\n      type: Input\n    }],\n    nzZeroTrigger: [{\n      type: Input\n    }],\n    nzTrigger: [{\n      type: Input\n    }],\n    matchBreakPoint: [{\n      type: Input\n    }],\n    nzCollapsedWidth: [{\n      type: Input\n    }],\n    siderWidth: [{\n      type: Input\n    }],\n    nzBreakpoint: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSiderComponent {\n  platform;\n  cdr;\n  breakpointService;\n  destroy$ = new Subject();\n  nzMenuDirective = null;\n  nzCollapsedChange = new EventEmitter();\n  nzWidth = 200;\n  nzTheme = 'dark';\n  nzCollapsedWidth = 80;\n  nzBreakpoint = null;\n  nzZeroTrigger = null;\n  nzTrigger = undefined;\n  nzReverseArrow = false;\n  nzCollapsible = false;\n  nzCollapsed = false;\n  matchBreakPoint = false;\n  flexSetting = null;\n  widthSetting = null;\n  updateStyleMap() {\n    this.widthSetting = this.nzCollapsed ? `${this.nzCollapsedWidth}px` : toCssPixel(this.nzWidth);\n    this.flexSetting = `0 0 ${this.widthSetting}`;\n    this.cdr.markForCheck();\n  }\n  updateMenuInlineCollapsed() {\n    if (this.nzMenuDirective && this.nzMenuDirective.nzMode === 'inline' && this.nzCollapsedWidth !== 0) {\n      this.nzMenuDirective.setInlineCollapsed(this.nzCollapsed);\n    }\n  }\n  setCollapsed(collapsed) {\n    if (collapsed !== this.nzCollapsed) {\n      this.nzCollapsed = collapsed;\n      this.nzCollapsedChange.emit(collapsed);\n      this.updateMenuInlineCollapsed();\n      this.updateStyleMap();\n      this.cdr.markForCheck();\n    }\n  }\n  constructor(platform, cdr, breakpointService) {\n    this.platform = platform;\n    this.cdr = cdr;\n    this.breakpointService = breakpointService;\n  }\n  ngOnInit() {\n    this.updateStyleMap();\n    if (this.platform.isBrowser) {\n      this.breakpointService.subscribe(siderResponsiveMap, true).pipe(takeUntil(this.destroy$)).subscribe(map => {\n        const breakpoint = this.nzBreakpoint;\n        if (breakpoint) {\n          inNextTick().subscribe(() => {\n            this.matchBreakPoint = !map[breakpoint];\n            this.setCollapsed(this.matchBreakPoint);\n            this.cdr.markForCheck();\n          });\n        }\n      });\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      nzCollapsed,\n      nzCollapsedWidth,\n      nzWidth\n    } = changes;\n    if (nzCollapsed || nzCollapsedWidth || nzWidth) {\n      this.updateStyleMap();\n    }\n    if (nzCollapsed) {\n      this.updateMenuInlineCollapsed();\n    }\n  }\n  ngAfterContentInit() {\n    this.updateMenuInlineCollapsed();\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzSiderComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSiderComponent)(i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.NzBreakpointService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzSiderComponent,\n    selectors: [[\"nz-sider\"]],\n    contentQueries: function NzSiderComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzMenuDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzMenuDirective = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-layout-sider\"],\n    hostVars: 18,\n    hostBindings: function NzSiderComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"flex\", ctx.flexSetting)(\"max-width\", ctx.widthSetting)(\"min-width\", ctx.widthSetting)(\"width\", ctx.widthSetting);\n        i0.ɵɵclassProp(\"ant-layout-sider-zero-width\", ctx.nzCollapsed && ctx.nzCollapsedWidth === 0)(\"ant-layout-sider-light\", ctx.nzTheme === \"light\")(\"ant-layout-sider-dark\", ctx.nzTheme === \"dark\")(\"ant-layout-sider-collapsed\", ctx.nzCollapsed)(\"ant-layout-sider-has-trigger\", ctx.nzCollapsible && ctx.nzTrigger !== null);\n      }\n    },\n    inputs: {\n      nzWidth: \"nzWidth\",\n      nzTheme: \"nzTheme\",\n      nzCollapsedWidth: \"nzCollapsedWidth\",\n      nzBreakpoint: \"nzBreakpoint\",\n      nzZeroTrigger: \"nzZeroTrigger\",\n      nzTrigger: \"nzTrigger\",\n      nzReverseArrow: [2, \"nzReverseArrow\", \"nzReverseArrow\", booleanAttribute],\n      nzCollapsible: [2, \"nzCollapsible\", \"nzCollapsible\", booleanAttribute],\n      nzCollapsed: [2, \"nzCollapsed\", \"nzCollapsed\", booleanAttribute]\n    },\n    outputs: {\n      nzCollapsedChange: \"nzCollapsedChange\"\n    },\n    exportAs: [\"nzSider\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"ant-layout-sider-children\"], [\"nz-sider-trigger\", \"\", 3, \"matchBreakPoint\", \"nzCollapsedWidth\", \"nzCollapsed\", \"nzBreakpoint\", \"nzReverseArrow\", \"nzTrigger\", \"nzZeroTrigger\", \"siderWidth\"], [\"nz-sider-trigger\", \"\", 3, \"click\", \"matchBreakPoint\", \"nzCollapsedWidth\", \"nzCollapsed\", \"nzBreakpoint\", \"nzReverseArrow\", \"nzTrigger\", \"nzZeroTrigger\", \"siderWidth\"]],\n    template: function NzSiderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(2, NzSiderComponent_Conditional_2_Template, 1, 8, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.nzCollapsible && ctx.nzTrigger !== null ? 2 : -1);\n      }\n    },\n    dependencies: [NzSiderTriggerComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSiderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-sider',\n      exportAs: 'nzSider',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div class=\"ant-layout-sider-children\">\n      <ng-content></ng-content>\n    </div>\n    @if (nzCollapsible && nzTrigger !== null) {\n      <div\n        nz-sider-trigger\n        [matchBreakPoint]=\"matchBreakPoint\"\n        [nzCollapsedWidth]=\"nzCollapsedWidth\"\n        [nzCollapsed]=\"nzCollapsed\"\n        [nzBreakpoint]=\"nzBreakpoint\"\n        [nzReverseArrow]=\"nzReverseArrow\"\n        [nzTrigger]=\"nzTrigger\"\n        [nzZeroTrigger]=\"nzZeroTrigger\"\n        [siderWidth]=\"widthSetting\"\n        (click)=\"setCollapsed(!nzCollapsed)\"\n      ></div>\n    }\n  `,\n      host: {\n        class: 'ant-layout-sider',\n        '[class.ant-layout-sider-zero-width]': `nzCollapsed && nzCollapsedWidth === 0`,\n        '[class.ant-layout-sider-light]': `nzTheme === 'light'`,\n        '[class.ant-layout-sider-dark]': `nzTheme === 'dark'`,\n        '[class.ant-layout-sider-collapsed]': `nzCollapsed`,\n        '[class.ant-layout-sider-has-trigger]': `nzCollapsible && nzTrigger !== null`,\n        '[style.flex]': 'flexSetting',\n        '[style.maxWidth]': 'widthSetting',\n        '[style.minWidth]': 'widthSetting',\n        '[style.width]': 'widthSetting'\n      },\n      imports: [NzSiderTriggerComponent]\n    }]\n  }], () => [{\n    type: i1$1.Platform\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.NzBreakpointService\n  }], {\n    nzMenuDirective: [{\n      type: ContentChild,\n      args: [NzMenuDirective]\n    }],\n    nzCollapsedChange: [{\n      type: Output\n    }],\n    nzWidth: [{\n      type: Input\n    }],\n    nzTheme: [{\n      type: Input\n    }],\n    nzCollapsedWidth: [{\n      type: Input\n    }],\n    nzBreakpoint: [{\n      type: Input\n    }],\n    nzZeroTrigger: [{\n      type: Input\n    }],\n    nzTrigger: [{\n      type: Input\n    }],\n    nzReverseArrow: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzCollapsible: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzCollapsed: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass NzLayoutComponent {\n  directionality;\n  listOfNzSiderComponent;\n  dir = 'ltr';\n  destroy$ = new Subject();\n  constructor(directionality) {\n    this.directionality = directionality;\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzLayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzLayoutComponent)(i0.ɵɵdirectiveInject(i1$2.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzLayoutComponent,\n    selectors: [[\"nz-layout\"]],\n    contentQueries: function NzLayoutComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzSiderComponent, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzSiderComponent = _t);\n      }\n    },\n    hostAttrs: [1, \"ant-layout\"],\n    hostVars: 4,\n    hostBindings: function NzLayoutComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-layout-rtl\", ctx.dir === \"rtl\")(\"ant-layout-has-sider\", ctx.listOfNzSiderComponent.length > 0);\n      }\n    },\n    exportAs: [\"nzLayout\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzLayoutComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-layout',\n      exportAs: 'nzLayout',\n      template: `<ng-content></ng-content>`,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      host: {\n        class: 'ant-layout',\n        '[class.ant-layout-rtl]': `dir === 'rtl'`,\n        '[class.ant-layout-has-sider]': 'listOfNzSiderComponent.length > 0'\n      }\n    }]\n  }], () => [{\n    type: i1$2.Directionality\n  }], {\n    listOfNzSiderComponent: [{\n      type: ContentChildren,\n      args: [NzSiderComponent]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzLayoutModule {\n  static ɵfac = function NzLayoutModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzLayoutModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzLayoutModule,\n    imports: [NzLayoutComponent, NzHeaderComponent, NzContentComponent, NzFooterComponent, NzSiderComponent, NzSiderTriggerComponent],\n    exports: [NzLayoutComponent, NzHeaderComponent, NzContentComponent, NzFooterComponent, NzSiderComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzSiderComponent, NzSiderTriggerComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzLayoutModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzLayoutComponent, NzHeaderComponent, NzContentComponent, NzFooterComponent, NzSiderComponent, NzSiderTriggerComponent],\n      exports: [NzLayoutComponent, NzHeaderComponent, NzContentComponent, NzFooterComponent, NzSiderComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzContentComponent, NzFooterComponent, NzHeaderComponent, NzLayoutComponent, NzLayoutModule, NzSiderComponent, NzSiderTriggerComponent as ɵNzSiderTriggerComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,SAAS,6DAA6D,IAAI,KAAK;AAAC;AAChF,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,eAAe,CAAC;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,wBAA2B,YAAY,CAAC;AAC9C,IAAG,WAAW,oBAAoB,OAAO,iBAAiB,qBAAqB;AAAA,EACjF;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAAC;AAChF,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,eAAe,CAAC;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,oBAAuB,YAAY,CAAC;AAC1C,IAAG,WAAW,oBAAoB,OAAO,aAAa,iBAAiB;AAAA,EACzE;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,cAAc,SAAS,OAAO;AAAA,EAC/D;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,cAAc,UAAU,MAAM;AAAA,EAC/D;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,8DAA8D,GAAG,GAAG,WAAW,CAAC;AAAA,EACxL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,iBAAiB,IAAI,CAAC;AAAA,EAChD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,+DAA+D;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,CAAC,OAAO,WAAW,CAAC;AAAA,IAChE,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,mBAAmB,OAAO,eAAe,EAAE,oBAAoB,OAAO,gBAAgB,EAAE,eAAe,OAAO,WAAW,EAAE,gBAAgB,OAAO,YAAY,EAAE,kBAAkB,OAAO,cAAc,EAAE,aAAa,OAAO,SAAS,EAAE,iBAAiB,OAAO,aAAa,EAAE,cAAc,OAAO,YAAY;AAAA,EAChU;AACF;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS,SAAS,KAAK,WAAW,eAAe,oBAAoB;AAAA,EAC5E;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAuB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,EAC9H;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,UAAU,CAAC,WAAW;AAAA,IACtB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS,SAAS,KAAK,WAAW,eAAe,mBAAmB;AAAA,EAC3E;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,EAC7H;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,UAAU,CAAC,UAAU;AAAA,IACrB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS,SAAS,KAAK,WAAW,eAAe,mBAAmB;AAAA,EAC3E;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,EAC7H;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,UAAU,CAAC,UAAU;AAAA,IACrB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,qBAAqB;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,oBAAoB;AAClB,SAAK,gBAAgB,KAAK,qBAAqB,MAAM,KAAK,gBAAgB,KAAK,mBAAmB,CAAC,KAAK;AACxG,SAAK,kBAAkB,KAAK,qBAAqB;AAAA,EACnD;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,SAAS,IAAI,kBAAkB,IAAI,aAAa,IAAI;AACnE,QAAG,YAAY,4BAA4B,IAAI,eAAe,EAAE,uCAAuC,IAAI,aAAa,EAAE,6CAA6C,IAAI,iBAAiB,IAAI,cAAc,EAAE,4CAA4C,IAAI,iBAAiB,CAAC,IAAI,cAAc;AAAA,MACtS;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,CAAC,gBAAgB;AAAA,IAC3B,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,UAAU,MAAM,CAAC;AAAA,IACvH,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,gDAAgD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAC1W;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,gBAAgB,IAAI,EAAE;AAC3C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,kBAAkB,IAAI,EAAE;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAkB,cAAiB,eAAe;AAAA,IACjE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBV,MAAM;AAAA,QACJ,oCAAoC;AAAA,QACpC,iBAAiB;AAAA,QACjB,+CAA+C;AAAA,QAC/C,qDAAqD;AAAA,QACrD,oDAAoD;AAAA,MACtD;AAAA,MACA,SAAS,CAAC,kBAAkB,YAAY;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB,IAAI,aAAa;AAAA,EACrC,UAAU;AAAA,EACV,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,iBAAiB;AACf,SAAK,eAAe,KAAK,cAAc,GAAG,KAAK,gBAAgB,OAAO,WAAW,KAAK,OAAO;AAC7F,SAAK,cAAc,OAAO,KAAK,YAAY;AAC3C,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,4BAA4B;AAC1B,QAAI,KAAK,mBAAmB,KAAK,gBAAgB,WAAW,YAAY,KAAK,qBAAqB,GAAG;AACnG,WAAK,gBAAgB,mBAAmB,KAAK,WAAW;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,aAAa,WAAW;AACtB,QAAI,cAAc,KAAK,aAAa;AAClC,WAAK,cAAc;AACnB,WAAK,kBAAkB,KAAK,SAAS;AACrC,WAAK,0BAA0B;AAC/B,WAAK,eAAe;AACpB,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,UAAU,KAAK,mBAAmB;AAC5C,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,WAAW;AACT,SAAK,eAAe;AACpB,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,kBAAkB,UAAU,oBAAoB,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,SAAO;AACzG,cAAM,aAAa,KAAK;AACxB,YAAI,YAAY;AACd,qBAAW,EAAE,UAAU,MAAM;AAC3B,iBAAK,kBAAkB,CAAC,IAAI,UAAU;AACtC,iBAAK,aAAa,KAAK,eAAe;AACtC,iBAAK,IAAI,aAAa;AAAA,UACxB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,eAAe,oBAAoB,SAAS;AAC9C,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,aAAa;AACf,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,kBAAuB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,EAClL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAAA,MACxE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,kBAAkB;AAAA,IACjC,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,WAAW,EAAE,aAAa,IAAI,YAAY,EAAE,aAAa,IAAI,YAAY,EAAE,SAAS,IAAI,YAAY;AAC/H,QAAG,YAAY,+BAA+B,IAAI,eAAe,IAAI,qBAAqB,CAAC,EAAE,0BAA0B,IAAI,YAAY,OAAO,EAAE,yBAAyB,IAAI,YAAY,MAAM,EAAE,8BAA8B,IAAI,WAAW,EAAE,gCAAgC,IAAI,iBAAiB,IAAI,cAAc,IAAI;AAAA,MAC7T;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA,MACX,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,IACjE;AAAA,IACA,SAAS;AAAA,MACP,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,2BAA2B,GAAG,CAAC,oBAAoB,IAAI,GAAG,mBAAmB,oBAAoB,eAAe,gBAAgB,kBAAkB,aAAa,iBAAiB,YAAY,GAAG,CAAC,oBAAoB,IAAI,GAAG,SAAS,mBAAmB,oBAAoB,eAAe,gBAAgB,kBAAkB,aAAa,iBAAiB,YAAY,CAAC;AAAA,IACpX,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,OAAO,CAAC;AAAA,MAC1E;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,iBAAiB,IAAI,cAAc,OAAO,IAAI,EAAE;AAAA,MACvE;AAAA,IACF;AAAA,IACA,cAAc,CAAC,uBAAuB;AAAA,IACtC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,uCAAuC;AAAA,QACvC,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,QACjC,sCAAsC;AAAA,QACtC,wCAAwC;AAAA,QACxC,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,MACnB;AAAA,MACA,SAAS,CAAC,uBAAuB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAsB,kBAAuB,cAAc,CAAC;AAAA,EAC/F;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAAA,MACjD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,yBAAyB;AAAA,MAC5E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,YAAY;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kBAAkB,IAAI,QAAQ,KAAK,EAAE,wBAAwB,IAAI,uBAAuB,SAAS,CAAC;AAAA,MACnH;AAAA,IACF;AAAA,IACA,UAAU,CAAC,UAAU;AAAA,IACrB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,mBAAmB,mBAAmB,oBAAoB,mBAAmB,kBAAkB,uBAAuB;AAAA,IAChI,SAAS,CAAC,mBAAmB,mBAAmB,oBAAoB,mBAAmB,gBAAgB;AAAA,EACzG,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,kBAAkB,uBAAuB;AAAA,EACrD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,mBAAmB,oBAAoB,mBAAmB,kBAAkB,uBAAuB;AAAA,MAChI,SAAS,CAAC,mBAAmB,mBAAmB,oBAAoB,mBAAmB,gBAAgB;AAAA,IACzG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}