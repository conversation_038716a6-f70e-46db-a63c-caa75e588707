{"version": 3, "sources": ["../../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-space.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError as _RuntimeError, ɵgetOutputDestroyRef as _getOutputDestroyRef, Injector, effect, untracked, ɵmicrotaskEffect as _microtaskEffect, assertNotInReactiveContext, signal, computed, PendingTasks, resource } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  source;\n  destroyed = false;\n  destroyRef = inject(DestroyRef);\n  constructor(source) {\n    this.source = source;\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new _RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi\n */\nfunction outputToObservable(ref) {\n  const destroyRef = _getOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\nfunction toObservableMicrotask(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = _microtaskEffect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  typeof ngDevMode !== 'undefined' && ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  let destroyUnregisterFn;\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    },\n    complete: () => {\n      destroyUnregisterFn?.();\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new _RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  destroyUnregisterFn = cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new _RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @experimental\n */\nfunction pendingUntilEvent(injector) {\n  if (injector === undefined) {\n    assertInInjectionContext(pendingUntilEvent);\n    injector = inject(Injector);\n  }\n  const taskService = injector.get(PendingTasks);\n  return sourceObservable => {\n    return new Observable(originalSubscriber => {\n      // create a new task on subscription\n      const removeTask = taskService.add();\n      let cleanedUp = false;\n      function cleanupTask() {\n        if (cleanedUp) {\n          return;\n        }\n        removeTask();\n        cleanedUp = true;\n      }\n      const innerSubscription = sourceObservable.subscribe({\n        next: v => {\n          originalSubscriber.next(v);\n          cleanupTask();\n        },\n        complete: () => {\n          originalSubscriber.complete();\n          cleanupTask();\n        },\n        error: e => {\n          originalSubscriber.error(e);\n          cleanupTask();\n        }\n      });\n      innerSubscription.add(() => {\n        originalSubscriber.unsubscribe();\n        cleanupTask();\n      });\n      return innerSubscription;\n    });\n  };\n}\nfunction rxResource(opts) {\n  opts?.injector || assertInInjectionContext(rxResource);\n  return resource({\n    ...opts,\n    loader: undefined,\n    stream: params => {\n      let sub;\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      params.abortSignal.addEventListener('abort', onAbort);\n      // Start off stream as undefined.\n      const stream = signal({\n        value: undefined\n      });\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      function send(value) {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      }\n      sub = opts.loader(params).subscribe({\n        next: value => send({\n          value\n        }),\n        error: error => {\n          send({\n            error\n          });\n          params.abortSignal.removeEventListener('abort', onAbort);\n        },\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new Error('Resource completed before producing a value')\n            });\n          }\n          params.abortSignal.removeEventListener('abort', onAbort);\n        }\n      });\n      return promise;\n    }\n  });\n}\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal, toObservableMicrotask as ɵtoObservableMicrotask };\n", "import { Directionality } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, input, booleanAttribute, inject, ElementRef, signal, ChangeDetectionStrategy, Component, computed, afterNextRender, Directive, TemplateRef, ContentChildren, Input, NgModule } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { __esDecorate, __runInitializers } from 'tslib';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzStringTemplateOutletDirective } from 'ng-zorro-antd/core/outlet';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction NzSpaceComponent_For_2_Conditional_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate(ctx_r0.nzSplit);\n  }\n}\nfunction NzSpaceComponent_For_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtemplate(1, NzSpaceComponent_For_2_Conditional_2_ng_template_1_Template, 1, 1, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const ɵ$index_2_r3 = ctx_r1.$index;\n    const ɵ$count_2_r4 = ctx_r1.$count;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"margin-block-end\", ctx_r0.nzDirection === \"vertical\" ? ɵ$index_2_r3 === ɵ$count_2_r4 - 1 ? null : ctx_r0.spaceSize : null, \"px\")(\"margin-inline-end\", ctx_r0.nzDirection === \"horizontal\" ? ɵ$index_2_r3 === ɵ$count_2_r4 - 1 ? null : ctx_r0.spaceSize : null, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzSplit)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c1, ɵ$index_2_r3));\n  }\n}\nfunction NzSpaceComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, NzSpaceComponent_For_2_Conditional_2_Template, 2, 8, \"span\", 2);\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ɵ$index_2_r3 = ctx.$index;\n    const ɵ$count_2_r4 = ctx.$count;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"margin-block-end\", ctx_r0.nzDirection === \"vertical\" ? ɵ$index_2_r3 === ɵ$count_2_r4 - 1 ? null : ctx_r0.spaceSize : null, \"px\")(\"margin-inline-end\", ctx_r0.nzDirection === \"horizontal\" ? ɵ$index_2_r3 === ɵ$count_2_r4 - 1 ? null : ctx_r0.spaceSize : null, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzSplit && !(ɵ$index_2_r3 === ɵ$count_2_r4 - 1) ? 2 : -1);\n  }\n}\nconst NZ_SPACE_COMPACT_SIZE = new InjectionToken('NZ_SPACE_COMPACT_SIZE');\nconst NZ_SPACE_COMPACT_ITEMS = new InjectionToken('NZ_SPACE_COMPACT_ITEMS');\nconst NZ_SPACE_COMPACT_ITEM_TYPE = new InjectionToken('NZ_SPACE_COMPACT_ITEM_TYPE');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSpaceCompactComponent {\n  nzBlock = input(false, {\n    transform: booleanAttribute\n  });\n  nzDirection = input('horizontal');\n  nzSize = input('default');\n  elementRef = inject(ElementRef);\n  static ɵfac = function NzSpaceCompactComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSpaceCompactComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzSpaceCompactComponent,\n    selectors: [[\"nz-space-compact\"]],\n    hostAttrs: [1, \"ant-space-compact\"],\n    hostVars: 4,\n    hostBindings: function NzSpaceCompactComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-space-compact-block\", ctx.nzBlock())(\"ant-space-compact-vertical\", ctx.nzDirection() === \"vertical\");\n      }\n    },\n    inputs: {\n      nzBlock: [1, \"nzBlock\"],\n      nzDirection: [1, \"nzDirection\"],\n      nzSize: [1, \"nzSize\"]\n    },\n    exportAs: [\"nzSpaceCompact\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NZ_SPACE_COMPACT_SIZE,\n      useFactory: () => inject(NzSpaceCompactComponent).nzSize\n    }, {\n      provide: NZ_SPACE_COMPACT_ITEMS,\n      useFactory: () => signal([])\n    }])],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzSpaceCompactComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpaceCompactComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-space-compact',\n      exportAs: 'nzSpaceCompact',\n      template: `<ng-content></ng-content>`,\n      host: {\n        class: 'ant-space-compact',\n        '[class.ant-space-compact-block]': `nzBlock()`,\n        '[class.ant-space-compact-vertical]': `nzDirection() === 'vertical'`\n      },\n      providers: [{\n        provide: NZ_SPACE_COMPACT_SIZE,\n        useFactory: () => inject(NzSpaceCompactComponent).nzSize\n      }, {\n        provide: NZ_SPACE_COMPACT_ITEMS,\n        useFactory: () => signal([])\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSpaceCompactItemDirective {\n  /**\n   * Ancestor component injected from the parent.\n   * Note that it is not necessarily the direct parent component.\n   */\n  spaceCompactCmp = inject(NzSpaceCompactComponent, {\n    host: true,\n    optional: true\n  });\n  items = inject(NZ_SPACE_COMPACT_ITEMS, {\n    host: true,\n    optional: true\n  });\n  type = inject(NZ_SPACE_COMPACT_ITEM_TYPE);\n  elementRef = inject(ElementRef);\n  directionality = inject(Directionality);\n  dir = toSignal(this.directionality.change, {\n    initialValue: this.directionality.value\n  });\n  get parentElement() {\n    return this.elementRef.nativeElement?.parentElement;\n  }\n  class = computed(() => {\n    // Only handle when the parent is space compact component\n    if (!this.spaceCompactCmp || !this.items) return null;\n    // Ensure that the injected ancestor component's elements are parent elements\n    if (this.parentElement !== this.spaceCompactCmp.elementRef.nativeElement) return null;\n    const items = this.items();\n    const direction = this.spaceCompactCmp.nzDirection();\n    const classes = [compactItemClassOf(this.type, direction, this.dir() === 'rtl')];\n    const index = items.indexOf(this);\n    const firstIndex = items.findIndex(element => element);\n    // Array [empty, item]\n    // In this case, the index of the first valid element is not 0,\n    // so we need to use findIndex to find the index value of the first valid element.\n    if (index === firstIndex) {\n      classes.push(compactFirstItemClassOf(this.type, direction));\n    } else if (index === items.length - 1) {\n      classes.push(compactLastItemClassOf(this.type, direction));\n    }\n    return classes;\n  });\n  constructor() {\n    if (!this.spaceCompactCmp || !this.items) return;\n    afterNextRender(() => {\n      // Ensure that the injected ancestor component's elements are parent elements\n      if (this.parentElement === this.spaceCompactCmp.elementRef.nativeElement) {\n        const index = Array.from(this.parentElement.children).indexOf(this.elementRef.nativeElement);\n        this.items.update(value => {\n          const newValue = value.slice();\n          newValue.splice(index, 0, this);\n          return newValue;\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.items?.update(value => value.filter(o => o !== this));\n  }\n  static ɵfac = function NzSpaceCompactItemDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSpaceCompactItemDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzSpaceCompactItemDirective,\n    hostVars: 2,\n    hostBindings: function NzSpaceCompactItemDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.class());\n      }\n    },\n    exportAs: [\"nzSpaceCompactItem\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpaceCompactItemDirective, [{\n    type: Directive,\n    args: [{\n      exportAs: 'nzSpaceCompactItem',\n      host: {\n        '[class]': 'class()'\n      }\n    }]\n  }], () => [], null);\n})();\nfunction generateCompactClass(type, direction, position) {\n  const directionPrefix = direction === 'vertical' ? 'vertical-' : '';\n  return `ant-${type}-compact-${directionPrefix}${position}`;\n}\nfunction compactItemClassOf(type, direction, rtl) {\n  const rtlSuffix = rtl ? '-rtl' : '';\n  return `${generateCompactClass(type, direction, 'item')}${rtlSuffix}`;\n}\nfunction compactFirstItemClassOf(type, direction) {\n  return generateCompactClass(type, direction, 'first-item');\n}\nfunction compactLastItemClassOf(type, direction) {\n  return generateCompactClass(type, direction, 'last-item');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSpaceItemDirective {\n  static ɵfac = function NzSpaceItemDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSpaceItemDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzSpaceItemDirective,\n    selectors: [[\"\", \"nzSpaceItem\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpaceItemDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzSpaceItem]'\n    }]\n  }], null, null);\n})();\nconst NZ_CONFIG_MODULE_NAME = 'space';\nconst SPACE_SIZE = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\nlet NzSpaceComponent = (() => {\n  let _nzSize_decorators;\n  let _nzSize_initializers = [];\n  let _nzSize_extraInitializers = [];\n  return class NzSpaceComponent {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzSize_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzSize_decorators, {\n        kind: \"field\",\n        name: \"nzSize\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzSize\" in obj,\n          get: obj => obj.nzSize,\n          set: (obj, value) => {\n            obj.nzSize = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzSize_initializers, _nzSize_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    nzConfigService;\n    cdr;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME;\n    nzDirection = 'horizontal';\n    nzAlign;\n    nzSplit = null;\n    nzWrap = false;\n    nzSize = __runInitializers(this, _nzSize_initializers, 'small');\n    items = __runInitializers(this, _nzSize_extraInitializers);\n    mergedAlign;\n    spaceSize = SPACE_SIZE.small;\n    destroy$ = new Subject();\n    constructor(nzConfigService, cdr) {\n      this.nzConfigService = nzConfigService;\n      this.cdr = cdr;\n    }\n    updateSpaceItems() {\n      const numberSize = typeof this.nzSize === 'string' ? SPACE_SIZE[this.nzSize] : this.nzSize;\n      this.spaceSize = numberSize / (this.nzSplit ? 2 : 1);\n      this.cdr.markForCheck();\n    }\n    ngOnChanges() {\n      this.updateSpaceItems();\n      this.mergedAlign = this.nzAlign === undefined && this.nzDirection === 'horizontal' ? 'center' : this.nzAlign;\n    }\n    ngOnDestroy() {\n      this.destroy$.next(true);\n      this.destroy$.complete();\n    }\n    ngAfterContentInit() {\n      this.updateSpaceItems();\n      this.items.changes.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.cdr.markForCheck();\n      });\n    }\n    static ɵfac = function NzSpaceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzSpaceComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSpaceComponent,\n      selectors: [[\"nz-space\"], [\"\", \"nz-space\", \"\"]],\n      contentQueries: function NzSpaceComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzSpaceItemDirective, 4, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      hostAttrs: [1, \"ant-space\"],\n      hostVars: 14,\n      hostBindings: function NzSpaceComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"flex-wrap\", ctx.nzWrap ? \"wrap\" : null);\n          i0.ɵɵclassProp(\"ant-space-horizontal\", ctx.nzDirection === \"horizontal\")(\"ant-space-vertical\", ctx.nzDirection === \"vertical\")(\"ant-space-align-start\", ctx.mergedAlign === \"start\")(\"ant-space-align-end\", ctx.mergedAlign === \"end\")(\"ant-space-align-center\", ctx.mergedAlign === \"center\")(\"ant-space-align-baseline\", ctx.mergedAlign === \"baseline\");\n        }\n      },\n      inputs: {\n        nzDirection: \"nzDirection\",\n        nzAlign: \"nzAlign\",\n        nzSplit: \"nzSplit\",\n        nzWrap: [2, \"nzWrap\", \"nzWrap\", booleanAttribute],\n        nzSize: \"nzSize\"\n      },\n      exportAs: [\"nzSpace\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"ant-space-item\"], [3, \"ngTemplateOutlet\"], [1, \"ant-space-split\", 3, \"margin-block-end\", \"margin-inline-end\"], [1, \"ant-space-split\"], [3, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"]],\n      template: function NzSpaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵrepeaterCreate(1, NzSpaceComponent_For_2_Template, 3, 6, null, null, i0.ɵɵrepeaterTrackByIdentity);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(ctx.items);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpaceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-space, [nz-space]',\n      exportAs: 'nzSpace',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-content></ng-content>\n    @for (item of items; track item; let last = $last; let index = $index) {\n      <div\n        class=\"ant-space-item\"\n        [style.margin-block-end.px]=\"nzDirection === 'vertical' ? (last ? null : spaceSize) : null\"\n        [style.margin-inline-end.px]=\"nzDirection === 'horizontal' ? (last ? null : spaceSize) : null\"\n      >\n        <ng-container [ngTemplateOutlet]=\"item\"></ng-container>\n      </div>\n      @if (nzSplit && !last) {\n        <span\n          class=\"ant-space-split\"\n          [style.margin-block-end.px]=\"nzDirection === 'vertical' ? (last ? null : spaceSize) : null\"\n          [style.margin-inline-end.px]=\"nzDirection === 'horizontal' ? (last ? null : spaceSize) : null\"\n        >\n          <ng-template [nzStringTemplateOutlet]=\"nzSplit\" [nzStringTemplateOutletContext]=\"{ $implicit: index }\">{{\n            nzSplit\n          }}</ng-template>\n        </span>\n      }\n    }\n  `,\n      host: {\n        class: 'ant-space',\n        '[class.ant-space-horizontal]': 'nzDirection === \"horizontal\"',\n        '[class.ant-space-vertical]': 'nzDirection === \"vertical\"',\n        '[class.ant-space-align-start]': 'mergedAlign === \"start\"',\n        '[class.ant-space-align-end]': 'mergedAlign === \"end\"',\n        '[class.ant-space-align-center]': 'mergedAlign === \"center\"',\n        '[class.ant-space-align-baseline]': 'mergedAlign === \"baseline\"',\n        '[style.flex-wrap]': 'nzWrap ? \"wrap\" : null'\n      },\n      imports: [NgTemplateOutlet, NzStringTemplateOutletDirective]\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    nzDirection: [{\n      type: Input\n    }],\n    nzAlign: [{\n      type: Input\n    }],\n    nzSplit: [{\n      type: Input\n    }],\n    nzWrap: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [NzSpaceItemDirective, {\n        read: TemplateRef\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSpaceModule {\n  static ɵfac = function NzSpaceModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSpaceModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzSpaceModule,\n    imports: [NzSpaceComponent, NzSpaceItemDirective, NzSpaceCompactComponent],\n    exports: [NzSpaceComponent, NzSpaceItemDirective, NzSpaceCompactComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpaceModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSpaceComponent, NzSpaceItemDirective, NzSpaceCompactComponent],\n      exports: [NzSpaceComponent, NzSpaceItemDirective, NzSpaceCompactComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_SPACE_COMPACT_ITEMS, NZ_SPACE_COMPACT_ITEM_TYPE, NZ_SPACE_COMPACT_SIZE, NzSpaceCompactComponent, NzSpaceCompactItemDirective, NzSpaceComponent, NzSpaceItemDirective, NzSpaceModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgMA,SAAS,SAAS,QAAQ,SAAS;AACjC,SAAO,cAAc,eAAe,aAAa,2BAA2B,UAAU,6JAAkK;AACxP,QAAM,kBAAkB,CAAC,SAAS;AAClC,qBAAmB,CAAC,SAAS,YAAY,yBAAyB,QAAQ;AAC1E,QAAM,aAAa,kBAAkB,SAAS,UAAU,IAAI,UAAU,KAAK,OAAO,UAAU,IAAI;AAChG,QAAM,QAAQ,kBAAkB,SAAS,KAAK;AAG9C,MAAI;AACJ,MAAI,SAAS,aAAa;AAExB,YAAQ,OAAO;AAAA,MACb,MAAM;AAAA;AAAA,IACR,GAAG;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AAEL,YAAQ,OAAO;AAAA,MACb,MAAM;AAAA,MACN,OAAO,SAAS;AAAA,IAClB,GAAG;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI;AAOJ,QAAM,MAAM,OAAO,UAAU;AAAA,IAC3B,MAAM,WAAS,MAAM,IAAI;AAAA,MACvB,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,IACD,OAAO,WAAS;AACd,UAAI,SAAS,cAAc;AAGzB,cAAM;AAAA,MACR;AACA,YAAM,IAAI;AAAA,QACR,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,UAAU,MAAM;AACd,4BAAsB;AAAA,IACxB;AAAA;AAAA;AAAA,EAGF,CAAC;AACD,MAAI,SAAS,eAAe,MAAM,EAAE,SAAS,GAA2B;AACtE,UAAM,IAAI,aAAc,MAA6D,OAAO,cAAc,eAAe,cAAc,qFAAqF;AAAA,EAC9N;AAEA,wBAAsB,YAAY,UAAU,IAAI,YAAY,KAAK,GAAG,CAAC;AAGrE,SAAO,SAAS,MAAM;AACpB,UAAM,UAAU,MAAM;AACtB,YAAQ,QAAQ,MAAM;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,cAAM,QAAQ;AAAA,MAChB,KAAK;AAEH,cAAM,IAAI,aAAc,MAA6D,OAAO,cAAc,eAAe,cAAc,qFAAqF;AAAA,IAChO;AAAA,EACF,GAAG;AAAA,IACD,OAAO,SAAS;AAAA,EAClB,CAAC;AACH;AACA,SAAS,kBAAkB,eAAe,OAAO,IAAI;AACnD,SAAO,CAAC,GAAG,MAAM,EAAE,SAAS,KAA2B,EAAE,SAAS,KAA2B,aAAa,EAAE,OAAO,EAAE,KAAK;AAC5H;;;AC9PA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,eAAe,OAAO;AAC5B,UAAM,eAAe,OAAO;AAC5B,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,oBAAoB,OAAO,gBAAgB,aAAa,iBAAiB,eAAe,IAAI,OAAO,OAAO,YAAY,MAAM,IAAI,EAAE,qBAAqB,OAAO,gBAAgB,eAAe,iBAAiB,eAAe,IAAI,OAAO,OAAO,YAAY,MAAM,IAAI;AACpR,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO,EAAE,iCAAoC,gBAAgB,GAAG,KAAK,YAAY,CAAC;AAAA,EACnI;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,QAAQ,CAAC;AAAA,EACjF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,eAAe,IAAI;AACzB,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,oBAAoB,OAAO,gBAAgB,aAAa,iBAAiB,eAAe,IAAI,OAAO,OAAO,YAAY,MAAM,IAAI,EAAE,qBAAqB,OAAO,gBAAgB,eAAe,iBAAiB,eAAe,IAAI,OAAO,OAAO,YAAY,MAAM,IAAI;AACpR,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,WAAW,EAAE,iBAAiB,eAAe,KAAK,IAAI,EAAE;AAAA,EAClF;AACF;AACA,IAAM,wBAAwB,IAAI,eAAe,uBAAuB;AACxE,IAAM,yBAAyB,IAAI,eAAe,wBAAwB;AAC1E,IAAM,6BAA6B,IAAI,eAAe,4BAA4B;AAMlF,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,UAAU,MAAM,OAAO;AAAA,IACrB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,cAAc,MAAM,YAAY;AAAA,EAChC,SAAS,MAAM,SAAS;AAAA,EACxB,aAAa,OAAO,UAAU;AAAA,EAC9B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,WAAW,CAAC,GAAG,mBAAmB;AAAA,IAClC,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,2BAA2B,IAAI,QAAQ,CAAC,EAAE,8BAA8B,IAAI,YAAY,MAAM,UAAU;AAAA,MACzH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,QAAQ,CAAC,GAAG,QAAQ;AAAA,IACtB;AAAA,IACA,UAAU,CAAC,gBAAgB;AAAA,IAC3B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,YAAY,MAAM,OAAO,wBAAuB,EAAE;AAAA,IACpD,GAAG;AAAA,MACD,SAAS;AAAA,MACT,YAAY,MAAM,OAAO,CAAC,CAAC;AAAA,IAC7B,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,mCAAmC;AAAA,QACnC,sCAAsC;AAAA,MACxC;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY,MAAM,OAAO,uBAAuB,EAAE;AAAA,MACpD,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY,MAAM,OAAO,CAAC,CAAC;AAAA,MAC7B,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,8BAAN,MAAM,6BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,kBAAkB,OAAO,yBAAyB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,QAAQ,OAAO,wBAAwB;AAAA,IACrC,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,0BAA0B;AAAA,EACxC,aAAa,OAAO,UAAU;AAAA,EAC9B,iBAAiB,OAAO,cAAc;AAAA,EACtC,MAAM,SAAS,KAAK,eAAe,QAAQ;AAAA,IACzC,cAAc,KAAK,eAAe;AAAA,EACpC,CAAC;AAAA,EACD,IAAI,gBAAgB;AAClB,WAAO,KAAK,WAAW,eAAe;AAAA,EACxC;AAAA,EACA,QAAQ,SAAS,MAAM;AAErB,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,MAAO,QAAO;AAEjD,QAAI,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,cAAe,QAAO;AACjF,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,YAAY,KAAK,gBAAgB,YAAY;AACnD,UAAM,UAAU,CAAC,mBAAmB,KAAK,MAAM,WAAW,KAAK,IAAI,MAAM,KAAK,CAAC;AAC/E,UAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,UAAM,aAAa,MAAM,UAAU,aAAW,OAAO;AAIrD,QAAI,UAAU,YAAY;AACxB,cAAQ,KAAK,wBAAwB,KAAK,MAAM,SAAS,CAAC;AAAA,IAC5D,WAAW,UAAU,MAAM,SAAS,GAAG;AACrC,cAAQ,KAAK,uBAAuB,KAAK,MAAM,SAAS,CAAC;AAAA,IAC3D;AACA,WAAO;AAAA,EACT,CAAC;AAAA,EACD,cAAc;AACZ,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,MAAO;AAC1C,oBAAgB,MAAM;AAEpB,UAAI,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,eAAe;AACxE,cAAM,QAAQ,MAAM,KAAK,KAAK,cAAc,QAAQ,EAAE,QAAQ,KAAK,WAAW,aAAa;AAC3F,aAAK,MAAM,OAAO,WAAS;AACzB,gBAAM,WAAW,MAAM,MAAM;AAC7B,mBAAS,OAAO,OAAO,GAAG,IAAI;AAC9B,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,OAAO,WAAS,MAAM,OAAO,OAAK,MAAM,IAAI,CAAC;AAAA,EAC3D;AAAA,EACA,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAA6B;AAAA,EAChE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU;AAAA,IACV,cAAc,SAAS,yCAAyC,IAAI,KAAK;AACvE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,MAAM,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,qBAAqB,MAAM,WAAW,UAAU;AACvD,QAAM,kBAAkB,cAAc,aAAa,cAAc;AACjE,SAAO,OAAO,IAAI,YAAY,eAAe,GAAG,QAAQ;AAC1D;AACA,SAAS,mBAAmB,MAAM,WAAW,KAAK;AAChD,QAAM,YAAY,MAAM,SAAS;AACjC,SAAO,GAAG,qBAAqB,MAAM,WAAW,MAAM,CAAC,GAAG,SAAS;AACrE;AACA,SAAS,wBAAwB,MAAM,WAAW;AAChD,SAAO,qBAAqB,MAAM,WAAW,YAAY;AAC3D;AACA,SAAS,uBAAuB,MAAM,WAAW;AAC/C,SAAO,qBAAqB,MAAM,WAAW,WAAW;AAC1D;AAMA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAwB;AAC9B,IAAM,aAAa;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB,MAAM;AAC5B,MAAI;AACJ,MAAI,uBAAuB,CAAC;AAC5B,MAAI,4BAA4B,CAAC;AACjC,SAAO,MAAMA,kBAAiB;AAAA,IAC5B,OAAO;AACL,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,2BAAqB,CAAC,WAAW,CAAC;AAClC,mBAAa,MAAM,MAAM,oBAAoB;AAAA,QAC3C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,YAAY;AAAA,UACxB,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,SAAS;AAAA,UACf;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,sBAAsB,yBAAyB;AAClD,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU;AAAA,QAC1D,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS,kBAAkB,MAAM,sBAAsB,OAAO;AAAA,IAC9D,QAAQ,kBAAkB,MAAM,yBAAyB;AAAA,IACzD;AAAA,IACA,YAAY,WAAW;AAAA,IACvB,WAAW,IAAI,QAAQ;AAAA,IACvB,YAAY,iBAAiB,KAAK;AAChC,WAAK,kBAAkB;AACvB,WAAK,MAAM;AAAA,IACb;AAAA,IACA,mBAAmB;AACjB,YAAM,aAAa,OAAO,KAAK,WAAW,WAAW,WAAW,KAAK,MAAM,IAAI,KAAK;AACpF,WAAK,YAAY,cAAc,KAAK,UAAU,IAAI;AAClD,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,IACA,cAAc;AACZ,WAAK,iBAAiB;AACtB,WAAK,cAAc,KAAK,YAAY,UAAa,KAAK,gBAAgB,eAAe,WAAW,KAAK;AAAA,IACvG;AAAA,IACA,cAAc;AACZ,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,SAAS,SAAS;AAAA,IACzB;AAAA,IACA,qBAAqB;AACnB,WAAK,iBAAiB;AACtB,WAAK,MAAM,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAChE,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,aAAO,KAAK,qBAAqBA,mBAAqB,kBAAqB,eAAe,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IACzI;AAAA,IACA,OAAO,OAAyB,kBAAkB;AAAA,MAChD,MAAMA;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,MAC9C,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,sBAAsB,GAAG,WAAW;AAAA,QAClE;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,IAAI,SAAS,SAAS,IAAI;AACtD,UAAG,YAAY,wBAAwB,IAAI,gBAAgB,YAAY,EAAE,sBAAsB,IAAI,gBAAgB,UAAU,EAAE,yBAAyB,IAAI,gBAAgB,OAAO,EAAE,uBAAuB,IAAI,gBAAgB,KAAK,EAAE,0BAA0B,IAAI,gBAAgB,QAAQ,EAAE,4BAA4B,IAAI,gBAAgB,UAAU;AAAA,QAC3V;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,QAChD,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,UAAU,CAAI,oBAAoB;AAAA,MAClC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,mBAAmB,GAAG,oBAAoB,mBAAmB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,CAAC;AAAA,MACnN,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,iBAAiB,GAAG,iCAAiC,GAAG,GAAG,MAAM,MAAS,yBAAyB;AAAA,QACxG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,IAAI,KAAK;AAAA,QACzB;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,+BAA+B;AAAA,MAChE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,gCAAgC;AAAA,QAChC,8BAA8B;AAAA,QAC9B,iCAAiC;AAAA,QACjC,+BAA+B;AAAA,QAC/B,kCAAkC;AAAA,QAClC,oCAAoC;AAAA,QACpC,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,CAAC,kBAAkB,+BAA+B;AAAA,IAC7D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,QAC3B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,kBAAkB,sBAAsB,uBAAuB;AAAA,IACzE,SAAS,CAAC,kBAAkB,sBAAsB,uBAAuB;AAAA,EAC3E,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,sBAAsB,uBAAuB;AAAA,MACzE,SAAS,CAAC,kBAAkB,sBAAsB,uBAAuB;AAAA,IAC3E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["NzSpaceComponent"]}