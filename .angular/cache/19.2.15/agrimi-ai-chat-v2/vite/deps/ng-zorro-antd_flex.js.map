{"version": 3, "sources": ["../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-flex.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { booleanAttribute, Input, Directive, NgModule } from '@angular/core';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFlexDirective {\n  nzVertical = false;\n  nzJustify = 'normal';\n  nzAlign = 'normal';\n  nzGap = 0;\n  nzWrap = 'nowrap';\n  nzFlex = 'unset';\n  get gap() {\n    switch (this.nzGap) {\n      case 'small':\n        return '8px';\n      case 'middle':\n        return '16px';\n      case 'large':\n        return '24px';\n      default:\n        if (typeof this.nzGap === 'number') {\n          return `${this.nzGap}px`;\n        }\n        return this.nzGap;\n    }\n  }\n  static ɵfac = function NzFlexDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFlexDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzFlexDirective,\n    selectors: [[\"\", \"nz-flex\", \"\"], [\"nz-flex\"]],\n    hostAttrs: [1, \"ant-flex\"],\n    hostVars: 60,\n    hostBindings: function NzFlexDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"gap\", ctx.gap)(\"flex\", ctx.nzFlex);\n        i0.ɵɵclassProp(\"ant-flex-vertical\", ctx.nzVertical)(\"ant-flex-justify-flex-start\", ctx.nzJustify === \"flex-start\")(\"ant-flex-justify-center\", ctx.nzJustify === \"center\")(\"ant-flex-justify-flex-end\", ctx.nzJustify === \"flex-end\")(\"ant-flex-justify-space-between\", ctx.nzJustify === \"space-between\")(\"ant-flex-justify-space-around\", ctx.nzJustify === \"space-around\")(\"ant-flex-justify-space-evenly\", ctx.nzJustify === \"space-evenly\")(\"ant-flex-justify-start\", ctx.nzJustify === \"start\")(\"ant-flex-justify-end\", ctx.nzJustify === \"end\")(\"ant-flex-justify-right\", ctx.nzJustify === \"right\")(\"ant-flex-justify-left\", ctx.nzJustify === \"left\")(\"ant-flex-justify-stretch\", ctx.nzJustify === \"stretch\")(\"ant-flex-justify-normal\", ctx.nzJustify === \"normal\")(\"ant-flex-align-flex-start\", ctx.nzAlign === \"flex-start\")(\"ant-flex-align-center\", ctx.nzAlign === \"center\")(\"ant-flex-align-flex-end\", ctx.nzAlign === \"flex-end\")(\"ant-flex-align-space-between\", ctx.nzAlign === \"space-between\")(\"ant-flex-align-space-around\", ctx.nzAlign === \"space-around\")(\"ant-flex-align-space-evenly\", ctx.nzAlign === \"space-evenly\")(\"ant-flex-align-start\", ctx.nzAlign === \"start\")(\"ant-flex-align-end\", ctx.nzAlign === \"end\")(\"ant-flex-align-right\", ctx.nzAlign === \"right\")(\"ant-flex-align-left\", ctx.nzAlign === \"left\")(\"ant-flex-align-stretch\", ctx.nzAlign === \"stretch\")(\"ant-flex-align-normal\", ctx.nzAlign === \"normal\")(\"ant-flex-wrap-wrap\", ctx.nzWrap === \"wrap\")(\"ant-flex-wrap-wrap-reverse\", ctx.nzWrap === \"wrap-reverse\")(\"ant-flex-wrap-nowrap\", ctx.nzWrap === \"nowrap\");\n      }\n    },\n    inputs: {\n      nzVertical: [2, \"nzVertical\", \"nzVertical\", booleanAttribute],\n      nzJustify: \"nzJustify\",\n      nzAlign: \"nzAlign\",\n      nzGap: \"nzGap\",\n      nzWrap: \"nzWrap\",\n      nzFlex: \"nzFlex\"\n    },\n    exportAs: [\"nzFlex\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFlexDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-flex],nz-flex',\n      exportAs: 'nzFlex',\n      host: {\n        class: 'ant-flex',\n        '[class.ant-flex-vertical]': `nzVertical`,\n        '[class.ant-flex-justify-flex-start]': `nzJustify === 'flex-start'`,\n        '[class.ant-flex-justify-center]': `nzJustify === 'center'`,\n        '[class.ant-flex-justify-flex-end]': `nzJustify === 'flex-end'`,\n        '[class.ant-flex-justify-space-between]': `nzJustify === 'space-between'`,\n        '[class.ant-flex-justify-space-around]': `nzJustify === 'space-around'`,\n        '[class.ant-flex-justify-space-evenly]': `nzJustify === 'space-evenly'`,\n        '[class.ant-flex-justify-start]': `nzJustify === 'start'`,\n        '[class.ant-flex-justify-end]': `nzJustify === 'end'`,\n        '[class.ant-flex-justify-right]': `nzJustify === 'right'`,\n        '[class.ant-flex-justify-left]': `nzJustify === 'left'`,\n        '[class.ant-flex-justify-stretch]': `nzJustify === 'stretch'`,\n        '[class.ant-flex-justify-normal]': `nzJustify === 'normal'`,\n        '[class.ant-flex-align-flex-start]': `nzAlign === 'flex-start'`,\n        '[class.ant-flex-align-center]': `nzAlign === 'center'`,\n        '[class.ant-flex-align-flex-end]': `nzAlign === 'flex-end'`,\n        '[class.ant-flex-align-space-between]': `nzAlign === 'space-between'`,\n        '[class.ant-flex-align-space-around]': `nzAlign === 'space-around'`,\n        '[class.ant-flex-align-space-evenly]': `nzAlign === 'space-evenly'`,\n        '[class.ant-flex-align-start]': `nzAlign === 'start'`,\n        '[class.ant-flex-align-end]': `nzAlign === 'end'`,\n        '[class.ant-flex-align-right]': `nzAlign === 'right'`,\n        '[class.ant-flex-align-left]': `nzAlign === 'left'`,\n        '[class.ant-flex-align-stretch]': `nzAlign === 'stretch'`,\n        '[class.ant-flex-align-normal]': `nzAlign === 'normal'`,\n        '[class.ant-flex-wrap-wrap]': `nzWrap === 'wrap'`,\n        '[class.ant-flex-wrap-wrap-reverse]': `nzWrap === 'wrap-reverse'`,\n        '[class.ant-flex-wrap-nowrap]': `nzWrap === 'nowrap'`,\n        '[style.gap]': `gap`,\n        '[style.flex]': `nzFlex`\n      }\n    }]\n  }], null, {\n    nzVertical: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzJustify: [{\n      type: Input\n    }],\n    nzAlign: [{\n      type: Input\n    }],\n    nzGap: [{\n      type: Input\n    }],\n    nzWrap: [{\n      type: Input\n    }],\n    nzFlex: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFlexModule {\n  static ɵfac = function NzFlexModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFlexModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzFlexModule,\n    imports: [NzFlexDirective],\n    exports: [NzFlexDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFlexModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzFlexDirective],\n      exports: [NzFlexDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzFlexDirective, NzFlexModule };\n"], "mappings": ";;;;;;;;;;;;;;AAOA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,IAAI,MAAM;AACR,YAAQ,KAAK,OAAO;AAAA,MAClB,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,YAAI,OAAO,KAAK,UAAU,UAAU;AAClC,iBAAO,GAAG,KAAK,KAAK;AAAA,QACtB;AACA,eAAO,KAAK;AAAA,IAChB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,GAAG,CAAC,SAAS,CAAC;AAAA,IAC5C,WAAW,CAAC,GAAG,UAAU;AAAA,IACzB,UAAU;AAAA,IACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,OAAO,IAAI,GAAG,EAAE,QAAQ,IAAI,MAAM;AACjD,QAAG,YAAY,qBAAqB,IAAI,UAAU,EAAE,+BAA+B,IAAI,cAAc,YAAY,EAAE,2BAA2B,IAAI,cAAc,QAAQ,EAAE,6BAA6B,IAAI,cAAc,UAAU,EAAE,kCAAkC,IAAI,cAAc,eAAe,EAAE,iCAAiC,IAAI,cAAc,cAAc,EAAE,iCAAiC,IAAI,cAAc,cAAc,EAAE,0BAA0B,IAAI,cAAc,OAAO,EAAE,wBAAwB,IAAI,cAAc,KAAK,EAAE,0BAA0B,IAAI,cAAc,OAAO,EAAE,yBAAyB,IAAI,cAAc,MAAM,EAAE,4BAA4B,IAAI,cAAc,SAAS,EAAE,2BAA2B,IAAI,cAAc,QAAQ,EAAE,6BAA6B,IAAI,YAAY,YAAY,EAAE,yBAAyB,IAAI,YAAY,QAAQ,EAAE,2BAA2B,IAAI,YAAY,UAAU,EAAE,gCAAgC,IAAI,YAAY,eAAe,EAAE,+BAA+B,IAAI,YAAY,cAAc,EAAE,+BAA+B,IAAI,YAAY,cAAc,EAAE,wBAAwB,IAAI,YAAY,OAAO,EAAE,sBAAsB,IAAI,YAAY,KAAK,EAAE,wBAAwB,IAAI,YAAY,OAAO,EAAE,uBAAuB,IAAI,YAAY,MAAM,EAAE,0BAA0B,IAAI,YAAY,SAAS,EAAE,yBAAyB,IAAI,YAAY,QAAQ,EAAE,sBAAsB,IAAI,WAAW,MAAM,EAAE,8BAA8B,IAAI,WAAW,cAAc,EAAE,wBAAwB,IAAI,WAAW,QAAQ;AAAA,MACnhD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,QAAQ;AAAA,EACrB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,mCAAmC;AAAA,QACnC,qCAAqC;AAAA,QACrC,0CAA0C;AAAA,QAC1C,yCAAyC;AAAA,QACzC,yCAAyC;AAAA,QACzC,kCAAkC;AAAA,QAClC,gCAAgC;AAAA,QAChC,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,QACjC,oCAAoC;AAAA,QACpC,mCAAmC;AAAA,QACnC,qCAAqC;AAAA,QACrC,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,QACnC,wCAAwC;AAAA,QACxC,uCAAuC;AAAA,QACvC,uCAAuC;AAAA,QACvC,gCAAgC;AAAA,QAChC,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,+BAA+B;AAAA,QAC/B,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,QACjC,8BAA8B;AAAA,QAC9B,sCAAsC;AAAA,QACtC,gCAAgC;AAAA,QAChC,eAAe;AAAA,QACf,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe;AAAA,IACzB,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe;AAAA,MACzB,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}