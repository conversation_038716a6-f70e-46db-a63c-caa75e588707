{"version": 3, "sources": ["../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-form.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/fake-event-detection-DWOdFTFz.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/focus-monitor-e2l_RpN3.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/private.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/observers.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/a11y-module-BYox5gpI.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/typeahead-9ZW4Dtsf.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/coercion/private.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/tree-key-manager-KnCoIkIC.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/a11y.mjs", "../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-input.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Input, ChangeDetectionStrategy, ViewEncapsulation, Component, NgModule } from '@angular/core';\nimport { ReplaySubject, BehaviorSubject } from 'rxjs';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzFormItemFeedbackIconComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.iconType);\n  }\n}\nclass NzFormStatusService {\n  formStatusChanges = new ReplaySubject(1);\n  static ɵfac = function NzFormStatusService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormStatusService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzFormStatusService,\n    factory: NzFormStatusService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormStatusService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// Used in input-group/input-number-group to make sure components in addon work well\nclass NzFormNoStatusService {\n  noFormStatus = new BehaviorSubject(false);\n  static ɵfac = function NzFormNoStatusService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormNoStatusService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzFormNoStatusService,\n    factory: NzFormNoStatusService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormNoStatusService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst iconTypeMap = {\n  error: 'close-circle-fill',\n  validating: 'loading',\n  success: 'check-circle-fill',\n  warning: 'exclamation-circle-fill'\n};\nclass NzFormItemFeedbackIconComponent {\n  cdr;\n  status = '';\n  constructor(cdr) {\n    this.cdr = cdr;\n  }\n  iconType = null;\n  ngOnChanges(_changes) {\n    this.updateIcon();\n  }\n  updateIcon() {\n    this.iconType = this.status ? iconTypeMap[this.status] : null;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function NzFormItemFeedbackIconComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormItemFeedbackIconComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFormItemFeedbackIconComponent,\n    selectors: [[\"nz-form-item-feedback-icon\"]],\n    hostAttrs: [1, \"ant-form-item-feedback-icon\"],\n    hostVars: 8,\n    hostBindings: function NzFormItemFeedbackIconComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-form-item-feedback-icon-error\", ctx.status === \"error\")(\"ant-form-item-feedback-icon-warning\", ctx.status === \"warning\")(\"ant-form-item-feedback-icon-success\", ctx.status === \"success\")(\"ant-form-item-feedback-icon-validating\", ctx.status === \"validating\");\n      }\n    },\n    inputs: {\n      status: \"status\"\n    },\n    exportAs: [\"nzFormFeedbackIcon\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"nzType\"]],\n    template: function NzFormItemFeedbackIconComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzFormItemFeedbackIconComponent_Conditional_0_Template, 1, 1, \"nz-icon\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.iconType ? 0 : -1);\n      }\n    },\n    dependencies: [NzIconModule, i1.NzIconDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormItemFeedbackIconComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-item-feedback-icon',\n      exportAs: 'nzFormFeedbackIcon',\n      imports: [NzIconModule],\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (iconType) {\n      <nz-icon [nzType]=\"iconType\" />\n    }\n  `,\n      host: {\n        class: 'ant-form-item-feedback-icon',\n        '[class.ant-form-item-feedback-icon-error]': 'status===\"error\"',\n        '[class.ant-form-item-feedback-icon-warning]': 'status===\"warning\"',\n        '[class.ant-form-item-feedback-icon-success]': 'status===\"success\"',\n        '[class.ant-form-item-feedback-icon-validating]': 'status===\"validating\"'\n      }\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    status: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * @deprecated Will be removed in v20. Use `NzFormItemFeedbackIconComponent` directly\n */\nclass NzFormPatchModule {\n  static ɵfac = function NzFormPatchModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormPatchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzFormPatchModule,\n    imports: [NzFormItemFeedbackIconComponent],\n    exports: [NzFormItemFeedbackIconComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzFormItemFeedbackIconComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormPatchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzFormItemFeedbackIconComponent],\n      exports: [NzFormItemFeedbackIconComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormPatchModule, NzFormStatusService };\n", "/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\nexport { isFakeTouchstartFromScreenReader as a, isFakeMousedownFromScreenReader as i };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { d as ALT, C as CONTROL, M as MAC_META, e as META, f as SHIFT } from './keycodes-CpHkExLC.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-esHZRgIN.mjs';\nimport { a as coerceElement } from './element-x4z00URv.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  _platform = inject(Platform);\n  _listenerCleanups;\n  /** Emits whenever an input modality is detected. */\n  modalityDetected;\n  /** Emits when the input modality changes. */\n  modalityChanged;\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n  _mostRecentTarget = null;\n  /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n  _modality = new BehaviorSubject(null);\n  /** Options for this InputModalityDetector. */\n  _options;\n  /**\n   * The timestamp of the last touch input modality. Used to determine whether mousedown events\n   * should be attributed to mouse or touch.\n   */\n  _lastTouchMs = 0;\n  /**\n   * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n   * bound.\n   */\n  _onKeydown = event => {\n    // If this is one of the keys we should ignore, then ignore it and don't update the input\n    // modality to keyboard.\n    if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n      return;\n    }\n    this._modality.next('keyboard');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onMousedown = event => {\n    // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n    // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n    // after the previous touch event.\n    if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n      return;\n    }\n    // Fake mousedown events are fired by some screen readers when controls are activated by the\n    // screen reader. Attribute them to keyboard input modality.\n    this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onTouchstart = event => {\n    // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n    // events are fired. Again, attribute to keyboard input modality.\n    if (isFakeTouchstartFromScreenReader(event)) {\n      this._modality.next('keyboard');\n      return;\n    }\n    // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n    // triggered via mouse vs touch.\n    this._lastTouchMs = Date.now();\n    this._modality.next('touch');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  constructor() {\n    const ngZone = inject(NgZone);\n    const document = inject(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {\n      optional: true\n    });\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [_bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions)];\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n  static ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputModalityDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputModalityDetector,\n    factory: InputModalityDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _inputModalityDetector = inject(InputModalityDetector);\n  /** The focus origin that the next focus event is a result of. */\n  _origin = null;\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  _lastFocusOrigin;\n  /** Whether the window has just been focused. */\n  _windowFocused = false;\n  /** The timeout id of the window focus timeout. */\n  _windowFocusTimeoutId;\n  /** The timeout id of the origin clearing timeout. */\n  _originTimeoutId;\n  /**\n   * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n   * focus events to touch interactions requires special logic.\n   */\n  _originFromTouchInteraction = false;\n  /** Map of elements being monitored to their info. */\n  _elementInfo = new Map();\n  /** The number of elements currently being monitored. */\n  _monitoredElementCount = 0;\n  /**\n   * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n   * as well as the number of monitored elements that they contain. We have to treat focus/blur\n   * handlers differently from the rest of the events, because the browser won't emit events\n   * to the document when focus moves inside of a shadow root.\n   */\n  _rootNodeFocusListenerCount = new Map();\n  /**\n   * The specified detection mode, used for attributing the origin of a focus\n   * event.\n   */\n  _detectionMode;\n  /**\n   * Event listener for `focus` events on the window.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _windowFocusListener = () => {\n    // Make a note of when the window regains focus, so we can\n    // restore the origin info for the focused element.\n    this._windowFocused = true;\n    this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false);\n  };\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** Subject for stopping our InputModalityDetector subscription. */\n  _stopInputModalityDetector = new Subject();\n  constructor() {\n    const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  /**\n   * Event listener for `focus` and 'blur' events on the document.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _rootNodeFocusAndBlurListener = event => {\n    const target = _getEventTarget(event);\n    // We need to walk up the ancestor chain in order to support `checkChildren`.\n    for (let element = target; element; element = element.parentElement) {\n      if (event.type === 'focus') {\n        this._onFocus(event, element);\n      } else {\n        this._onBlur(event, element);\n      }\n    }\n  };\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusMonitor,\n    factory: FocusMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  _elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _monitorSubscription;\n  _focusOrigin = null;\n  cdkFocusChange = new EventEmitter();\n  constructor() {}\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkMonitorFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkMonitorFocus,\n    selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n    outputs: {\n      cdkFocusChange: \"cdkFocusChange\"\n    },\n    exportAs: [\"cdkMonitorFocus\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus'\n    }]\n  }], () => [], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\nexport { CdkMonitorFocus as C, FocusMonitor as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FocusMonitorDetectionMode as c, FOCUS_MONITOR_DEFAULT_OPTIONS as d };\n", "export { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nclass _VisuallyHiddenLoader {\n  static ɵfac = function _VisuallyHiddenLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _VisuallyHiddenLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _VisuallyHiddenLoader,\n    selectors: [[\"ng-component\"]],\n    exportAs: [\"cdkVisuallyHidden\"],\n    decls: 0,\n    vars: 0,\n    template: function _VisuallyHiddenLoader_Template(rf, ctx) {},\n    styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_VisuallyHiddenLoader, [{\n    type: Component,\n    args: [{\n      exportAs: 'cdkVisuallyHidden',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _VisuallyHiddenLoader };\n", "import * as i0 from '@angular/core';\nimport { Injectable, inject, Ng<PERSON>one, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MutationObserverFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MutationObserverFactory,\n    factory: MutationObserverFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  _mutationObserverFactory = inject(MutationObserverFactory);\n  /** Keeps track of the existing MutationObservers so they can be reused. */\n  _observedElements = new Map();\n  _ngZone = inject(NgZone);\n  constructor() {}\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContentObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ContentObserver,\n    factory: ContentObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  _contentObserver = inject(ContentObserver);\n  _elementRef = inject(ElementRef);\n  /** Event emitted for each change in the element's content. */\n  event = new EventEmitter();\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  _disabled = false;\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  _debounce;\n  _currentSubscription = null;\n  constructor() {}\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkObserveContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkObserveContent,\n    selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n    inputs: {\n      disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n      debounce: \"debounce\"\n    },\n    outputs: {\n      event: \"cdkObserveContent\"\n    },\n    exportAs: [\"cdkObserveContent\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], () => [], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ObserversModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ObserversModule,\n    imports: [CdkObserveContent],\n    exports: [CdkObserveContent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MutationObserverFactory]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n", "import * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-e2l_RpN3.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-CljOfYGy.mjs';\nimport { ContentObserver, ObserversModule } from './observers.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  _platform = inject(Platform);\n  constructor() {}\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InteractivityChecker)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InteractivityChecker,\n    factory: InteractivityChecker.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  _element;\n  _checker;\n  _ngZone;\n  _document;\n  _injector;\n  _startAnchor;\n  _endAnchor;\n  _hasAttached = false;\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  startAnchorListener = () => this.focusLastTabbableElement();\n  endAnchorListener = () => this.focusFirstTabbableElement();\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  _enabled = true;\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _injector = inject(Injector);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n  static ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapFactory,\n    factory: FocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  /** Underlying FocusTrap instance. */\n  focusTrap;\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  _previouslyFocusedElement = null;\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  autoCapture;\n  constructor() {\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTrapFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTrapFocus,\n    selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n    inputs: {\n      enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n      autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n    },\n    exportAs: [\"cdkTrapFocus\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], () => [], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  _ngZone = inject(NgZone);\n  _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _liveElement;\n  _document = inject(DOCUMENT);\n  _previousTimeout;\n  _currentPromise;\n  _currentResolve;\n  constructor() {\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {\n      optional: true\n    });\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LiveAnnouncer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LiveAnnouncer,\n    factory: LiveAnnouncer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  _elementRef = inject(ElementRef);\n  _liveAnnouncer = inject(LiveAnnouncer);\n  _contentObserver = inject(ContentObserver);\n  _ngZone = inject(NgZone);\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  _politeness = 'polite';\n  /** Time in milliseconds after which to clear out the announcer element. */\n  duration;\n  _previousAnnouncedText;\n  _subscription;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAriaLive)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAriaLive,\n    selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n    inputs: {\n      politeness: [0, \"cdkAriaLive\", \"politeness\"],\n      duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n    },\n    exportAs: [\"cdkAriaLive\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], () => [], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  _platform = inject(Platform);\n  /**\n   * Figuring out the high contrast mode and adding the body classes can cause\n   * some expensive layouts. This flag is used to ensure that we only do it once.\n   */\n  _hasCheckedHighContrastMode;\n  _document = inject(DOCUMENT);\n  _breakpointSubscription;\n  constructor() {\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HighContrastModeDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HighContrastModeDetector,\n    factory: HighContrastModeDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || A11yModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: A11yModule,\n    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ObserversModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [], null);\n})();\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrapFactory as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, FocusTrap as a, HighContrastMode as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };\n", "import { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { A, Z, b as ZERO, N as NINE } from './keycodes-CpHkExLC.mjs';\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  _letterKeyStream = new Subject();\n  _items = [];\n  _selectedItemIndex = -1;\n  /** Buffer for the letters that the user has pressed */\n  _pressedLetters = [];\n  _skipPredicateFn;\n  _selectedItem = new Subject();\n  selectedItem = this._selectedItem;\n  constructor(initialItems, config) {\n    const typeAheadInterval = typeof config?.debounceInterval === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!this._skipPredicateFn?.(item) && item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\nexport { Typeahead as T };\n", "import { isObservable, of } from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nfunction coerceObservable(data) {\n  if (!isObservable(data)) {\n    return of(data);\n  }\n  return data;\n}\nexport { coerceObservable };\n", "import { QueryList, InjectionToken } from '@angular/core';\nimport { Subscription, isObservable, Subject, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n  /** The index of the currently active (focused) item. */\n  _activeItemIndex = -1;\n  /** The currently active (focused) item. */\n  _activeItem = null;\n  /** Whether or not we activate the item when it's focused. */\n  _shouldActivationFollowFocus = false;\n  /**\n   * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n   * Right arrow are switched.\n   */\n  _horizontalOrientation = 'ltr';\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager.\n   *\n   * The default value for this doesn't skip any elements in order to keep tree items focusable\n   * when disabled. This aligns with ARIA guidelines:\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n   */\n  _skipPredicateFn = _item => false;\n  /** Function to determine equivalent items. */\n  _trackByFn = item => item;\n  /** Synchronous cache of the items to manage. */\n  _items = [];\n  _typeahead;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _hasInitialFocused = false;\n  _initializeFocus() {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n    const activeItem = this._items[activeIndex];\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n    this._hasInitialFocused = true;\n  }\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items, config) {\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe(newItems => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n  /** Stream that emits any time the focused item changes. */\n  change = new Subject();\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event) {\n    const key = event.key;\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl' ? this._collapseCurrentItem() : this._expandCurrentItem();\n        break;\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl' ? this._expandCurrentItem() : this._collapseCurrentItem();\n        break;\n      case 'Home':\n        this._focusFirstItem();\n        break;\n      case 'End':\n        this._focusLastItem();\n        break;\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  getActiveItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The currently active item. */\n  getActiveItem() {\n    return this._activeItem;\n  }\n  /** Focus the first available item. */\n  _focusFirstItem() {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n  /** Focus the last available item. */\n  _focusLastItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n  /** Focus the next available item. */\n  _focusNextItem() {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n  /** Focus the previous available item. */\n  _focusPreviousItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n  focusItem(itemOrIndex, options = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n    let index = typeof itemOrIndex === 'number' ? itemOrIndex : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n    // If we're just setting the same item, don't re-call activate or focus\n    if (this._activeItem !== null && this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n      return;\n    }\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n  _updateActiveItemIndex(newItems) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n    const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n  _setTypeAhead(debounceInterval) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n  _findNextAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  _findPreviousAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent)) {\n        return;\n      }\n      this.focusItem(parent);\n    }\n  }\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren()).pipe(take(1)).subscribe(children => {\n        const firstChild = children.find(child => !this._skipPredicateFn(child));\n        if (!firstChild) {\n          return;\n        }\n        this.focusItem(firstChild);\n      });\n    }\n  }\n  _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean' ? this._activeItem.isExpanded : this._activeItem.isExpanded();\n  }\n  _isItemDisabled(item) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n  /** For all items that are the same level as the current item, we expand those items. */\n  _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n  _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction TREE_KEY_MANAGER_FACTORY() {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY\n};\nexport { TREE_KEY_MANAGER as T, TreeKeyManager as a, TREE_KEY_MANAGER_FACTORY as b, TREE_KEY_MANAGER_FACTORY_PROVIDER as c };\n", "export { C as CdkMonitorFocus, d as FOCUS_MONITOR_DEFAULT_OPTIONS, F as FocusMonitor, c as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-e2l_RpN3.mjs';\nimport { a as FocusTrap, I as InteractivityChecker } from './a11y-module-BYox5gpI.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrapFactory, b as HighContrastMode, H as HighContrastModeDetector, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-BYox5gpI.mjs';\nexport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-DC3-fwQI.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-C1rAQJ5z.mjs';\nexport { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nexport { b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-KnCoIkIC.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport 'rxjs/operators';\nimport './keycodes-CpHkExLC.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport './element-x4z00URv.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes.mjs';\nimport './coercion/private.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT);\n  /** Map of all registered message elements that have been placed into the document. */\n  _messageRegistry = new Map();\n  /** Container for all registered messages. */\n  _messagesContainer = null;\n  /** Unique ID for the service. */\n  _id = `${nextId++}`;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AriaDescriber)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AriaDescriber,\n    factory: AriaDescriber.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  _isNoopTreeKeyManager = true;\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  change = new Subject();\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  _focusTrapManager;\n  _inertStrategy;\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  /** Focus event handler. */\n  _listener = null;\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  // A stack of the FocusTraps on the page. Only the FocusTrap at the\n  // top of the stack is active.\n  _focusTrapStack = [];\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapManager)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapManager,\n    factory: FocusTrapManager.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _focusTrapManager = inject(FocusTrapManager);\n  _document = inject(DOCUMENT);\n  _inertStrategy;\n  _injector = inject(Injector);\n  constructor() {\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {\n      optional: true\n    });\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n  static ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ConfigurableFocusTrapFactory,\n    factory: ConfigurableFocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, FocusTrap, InteractivityChecker, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };\n", "import * as i0 from '@angular/core';\nimport { inject, ElementRef, Input, Directive, ChangeDetectionStrategy, ViewEncapsulation, Component, computed, signal, booleanAttribute, ContentChildren, forwardRef, numberAttribute, ViewChildren, isDevMode, ContentChild, NgModule } from '@angular/core';\nimport { Subject, merge, EMPTY } from 'rxjs';\nimport { takeUntil, distinctUntilChanged, filter, startWith, switchMap, mergeMap, map, tap } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport * as i2 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport * as i2$1 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { NzFormStatusService, NzFormNoStatusService, NzFormItemFeedbackIconComponent } from 'ng-zorro-antd/core/form';\nimport { getStatusClassNames, isNotNil } from 'ng-zorro-antd/core/util';\nimport * as i2$2 from 'ng-zorro-antd/space';\nimport { NZ_SPACE_COMPACT_SIZE, NZ_SPACE_COMPACT_ITEM_TYPE, NzSpaceCompactItemDirective } from 'ng-zorro-antd/space';\nimport * as i1$4 from '@angular/forms';\nimport { NgControl, Validators, ReactiveFormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1$2 from '@angular/cdk/bidi';\nimport * as i1$3 from '@angular/cdk/a11y';\nimport { BACKSPACE } from '@angular/cdk/keycodes';\nconst _c0 = [\"nz-input-group-slot\", \"\"];\nconst _c1 = [\"*\"];\nfunction NzInputGroupSlotComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.icon);\n  }\n}\nfunction NzInputGroupSlotComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.template);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzAddOnBeforeIcon)(\"template\", ctx_r0.nzAddOnBefore);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_2_ng_template_1_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_Conditional_0_Conditional_2_ng_template_1_Template, 0, 0, \"ng-template\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const affixTemplate_r2 = i0.ɵɵreference(3);\n    i0.ɵɵclassMap(ctx_r0.affixInGroupStatusCls);\n    i0.ɵɵclassProp(\"ant-input-affix-wrapper-disabled\", ctx_r0.disabled)(\"ant-input-affix-wrapper-sm\", ctx_r0.isSmall)(\"ant-input-affix-wrapper-lg\", ctx_r0.isLarge)(\"ant-input-affix-wrapper-focused\", ctx_r0.focused);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", affixTemplate_r2);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_3_ng_template_0_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_0_Conditional_3_ng_template_0_Template, 0, 0, \"ng-template\", 5);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const contentTemplate_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r3);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzAddOnAfterIcon)(\"template\", ctx_r0.nzAddOnAfter);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_Conditional_0_Conditional_1_Template, 1, 2, \"span\", 3)(2, NzInputGroupComponent_Conditional_0_Conditional_2_Template, 2, 11, \"span\", 4)(3, NzInputGroupComponent_Conditional_0_Conditional_3_Template, 1, 1, null, 5)(4, NzInputGroupComponent_Conditional_0_Conditional_4_Template, 1, 2, \"span\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzAddOnBefore || ctx_r0.nzAddOnBeforeIcon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isAffix || ctx_r0.hasFeedback ? 2 : 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.nzAddOnAfter || ctx_r0.nzAddOnAfterIcon ? 4 : -1);\n  }\n}\nfunction NzInputGroupComponent_Conditional_1_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_1_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 5);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const affixTemplate_r2 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", affixTemplate_r2);\n  }\n}\nfunction NzInputGroupComponent_Conditional_1_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_1_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 5);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const contentTemplate_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r3);\n  }\n}\nfunction NzInputGroupComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_1_Conditional_0_Template, 1, 1, null, 5)(1, NzInputGroupComponent_Conditional_1_Conditional_1_Template, 1, 1, null, 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.isAffix ? 0 : 1);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzPrefixIcon)(\"template\", ctx_r0.nzPrefix);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_ng_template_1_Template(rf, ctx) {}\nfunction NzInputGroupComponent_ng_template_2_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-form-item-feedback-icon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"status\", ctx_r0.status);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_ng_template_2_Conditional_2_Conditional_1_Template, 1, 1, \"nz-form-item-feedback-icon\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzSuffixIcon)(\"template\", ctx_r0.nzSuffix);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isFeedback ? 1 : -1);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_ng_template_2_Conditional_0_Template, 1, 2, \"span\", 7)(1, NzInputGroupComponent_ng_template_2_ng_template_1_Template, 0, 0, \"ng-template\", 5)(2, NzInputGroupComponent_ng_template_2_Conditional_2_Template, 2, 3, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const contentTemplate_r3 = i0.ɵɵreference(5);\n    i0.ɵɵconditional(ctx_r0.nzPrefix || ctx_r0.nzPrefixIcon ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzSuffix || ctx_r0.nzSuffixIcon || ctx_r0.isFeedback ? 2 : -1);\n  }\n}\nfunction NzInputGroupComponent_ng_template_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵelement(1, \"nz-form-item-feedback-icon\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"status\", ctx_r0.status);\n  }\n}\nfunction NzInputGroupComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_ng_template_4_Conditional_1_Template, 2, 1, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r0.isAddOn && !ctx_r0.isAffix && ctx_r0.isFeedback ? 1 : -1);\n  }\n}\nconst _c2 = [\"otpInput\"];\nfunction NzInputOtpComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 2, 0);\n    i0.ɵɵlistener(\"input\", function NzInputOtpComponent_For_1_Template_input_input_0_listener($event) {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInput($index_r2, $event));\n    })(\"focus\", function NzInputOtpComponent_For_1_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFocus($event));\n    })(\"keydown\", function NzInputOtpComponent_For_1_Template_input_keydown_0_listener($event) {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($index_r2, $event));\n    })(\"paste\", function NzInputOtpComponent_For_1_Template_input_paste_0_listener($event) {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPaste($index_r2, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzSize\", ctx_r2.nzSize)(\"formControl\", item_r4)(\"nzStatus\", ctx_r2.nzStatus);\n  }\n}\nconst _c3 = [[[\"textarea\", \"nz-input\", \"\"]]];\nconst _c4 = [\"textarea[nz-input]\"];\nclass NzAutosizeDirective {\n  ngZone;\n  platform;\n  resizeService;\n  autosize = false;\n  el = inject(ElementRef).nativeElement;\n  cachedLineHeight;\n  previousValue;\n  previousMinRows;\n  minRows;\n  maxRows;\n  maxHeight = null;\n  minHeight = null;\n  destroy$ = new Subject();\n  inputGap = 10;\n  set nzAutosize(value) {\n    const isAutoSizeType = data => typeof data !== 'string' && typeof data !== 'boolean' && (!!data.maxRows || !!data.minRows);\n    if (typeof value === 'string' || value === true) {\n      this.autosize = true;\n    } else if (isAutoSizeType(value)) {\n      this.autosize = true;\n      this.minRows = value.minRows;\n      this.maxRows = value.maxRows;\n      this.maxHeight = this.setMaxHeight();\n      this.minHeight = this.setMinHeight();\n    }\n  }\n  resizeToFitContent(force = false) {\n    this.cacheTextareaLineHeight();\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this.cachedLineHeight) {\n      return;\n    }\n    const textarea = this.el;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this.minRows === this.previousMinRows && value === this.previousValue) {\n      return;\n    }\n    const placeholderText = textarea.placeholder;\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    // Long placeholders that are wider than the textarea width may lead to a bigger scrollHeight\n    // value. To ensure that the scrollHeight is not bigger than the content, the placeholders\n    // need to be removed temporarily.\n    textarea.classList.add('nz-textarea-autosize-measuring');\n    textarea.placeholder = '';\n    let height = Math.round((textarea.scrollHeight - this.inputGap) / this.cachedLineHeight) * this.cachedLineHeight + this.inputGap;\n    if (this.maxHeight !== null && height > this.maxHeight) {\n      height = this.maxHeight;\n    }\n    if (this.minHeight !== null && height < this.minHeight) {\n      height = this.minHeight;\n    }\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    textarea.classList.remove('nz-textarea-autosize-measuring');\n    textarea.placeholder = placeholderText;\n    // On Firefox resizing the textarea will prevent it from scrolling to the caret position.\n    // We need to re-set the selection in order for it to scroll to the proper position.\n    if (typeof requestAnimationFrame !== 'undefined') {\n      this.ngZone.runOutsideAngular(() => requestAnimationFrame(() => {\n        const {\n          selectionStart,\n          selectionEnd\n        } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this.destroy$.isStopped && document.activeElement === textarea) {\n          textarea.setSelectionRange(selectionStart, selectionEnd);\n        }\n      }));\n    }\n    this.previousValue = value;\n    this.previousMinRows = this.minRows;\n  }\n  cacheTextareaLineHeight() {\n    if (this.cachedLineHeight >= 0 || !this.el.parentNode) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    const textareaClone = this.el.cloneNode(false);\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    textareaClone.style.position = 'absolute';\n    textareaClone.style.visibility = 'hidden';\n    textareaClone.style.border = 'none';\n    textareaClone.style.padding = '0';\n    textareaClone.style.height = '';\n    textareaClone.style.minHeight = '';\n    textareaClone.style.maxHeight = '';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    textareaClone.style.overflow = 'hidden';\n    this.el.parentNode.appendChild(textareaClone);\n    this.cachedLineHeight = textareaClone.clientHeight - this.inputGap;\n    this.el.parentNode.removeChild(textareaClone);\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this.maxHeight = this.setMaxHeight();\n    this.minHeight = this.setMinHeight();\n  }\n  setMinHeight() {\n    const minHeight = this.minRows && this.cachedLineHeight ? this.minRows * this.cachedLineHeight + this.inputGap : null;\n    if (minHeight !== null) {\n      this.el.style.minHeight = `${minHeight}px`;\n    }\n    return minHeight;\n  }\n  setMaxHeight() {\n    const maxHeight = this.maxRows && this.cachedLineHeight ? this.maxRows * this.cachedLineHeight + this.inputGap : null;\n    if (maxHeight !== null) {\n      this.el.style.maxHeight = `${maxHeight}px`;\n    }\n    return maxHeight;\n  }\n  noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  constructor(ngZone, platform, resizeService) {\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.resizeService = resizeService;\n  }\n  ngAfterViewInit() {\n    if (this.autosize && this.platform.isBrowser) {\n      this.resizeToFitContent();\n      this.resizeService.subscribe().pipe(takeUntil(this.destroy$)).subscribe(() => this.resizeToFitContent(true));\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  ngDoCheck() {\n    if (this.autosize && this.platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  static ɵfac = function NzAutosizeDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzAutosizeDirective)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i2.NzResizeService));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzAutosizeDirective,\n    selectors: [[\"textarea\", \"nzAutosize\", \"\"]],\n    hostAttrs: [\"rows\", \"1\"],\n    hostBindings: function NzAutosizeDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function NzAutosizeDirective_input_HostBindingHandler() {\n          return ctx.noopInputHandler();\n        });\n      }\n    },\n    inputs: {\n      nzAutosize: \"nzAutosize\"\n    },\n    exportAs: [\"nzAutosize\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAutosizeDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[nzAutosize]',\n      exportAs: 'nzAutosize',\n      host: {\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        rows: '1',\n        '(input)': 'noopInputHandler()'\n      }\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.Platform\n  }, {\n    type: i2.NzResizeService\n  }], {\n    nzAutosize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputAddonBeforeDirective {\n  static ɵfac = function NzInputAddonBeforeDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputAddonBeforeDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzInputAddonBeforeDirective,\n    selectors: [[\"\", \"nzInputAddonBefore\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputAddonBeforeDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzInputAddonBefore]'\n    }]\n  }], null, null);\n})();\nclass NzInputAddonAfterDirective {\n  static ɵfac = function NzInputAddonAfterDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputAddonAfterDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzInputAddonAfterDirective,\n    selectors: [[\"\", \"nzInputAddonAfter\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputAddonAfterDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzInputAddonAfter]'\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputPrefixDirective {\n  static ɵfac = function NzInputPrefixDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputPrefixDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzInputPrefixDirective,\n    selectors: [[\"\", \"nzInputPrefix\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputPrefixDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzInputPrefix]'\n    }]\n  }], null, null);\n})();\nclass NzInputSuffixDirective {\n  static ɵfac = function NzInputSuffixDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputSuffixDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzInputSuffixDirective,\n    selectors: [[\"\", \"nzInputSuffix\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputSuffixDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzInputSuffix]'\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputGroupSlotComponent {\n  icon = null;\n  type = null;\n  template = null;\n  static ɵfac = function NzInputGroupSlotComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputGroupSlotComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzInputGroupSlotComponent,\n    selectors: [[\"\", \"nz-input-group-slot\", \"\"]],\n    hostVars: 6,\n    hostBindings: function NzInputGroupSlotComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-input-group-addon\", ctx.type === \"addon\")(\"ant-input-prefix\", ctx.type === \"prefix\")(\"ant-input-suffix\", ctx.type === \"suffix\");\n      }\n    },\n    inputs: {\n      icon: \"icon\",\n      type: \"type\",\n      template: \"template\"\n    },\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 3,\n    vars: 2,\n    consts: [[3, \"nzType\"], [4, \"nzStringTemplateOutlet\"]],\n    template: function NzInputGroupSlotComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzInputGroupSlotComponent_Conditional_0_Template, 1, 1, \"nz-icon\", 0)(1, NzInputGroupSlotComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n        i0.ɵɵprojection(2);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.icon ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.template);\n      }\n    },\n    dependencies: [NzIconModule, i1$1.NzIconDirective, NzOutletModule, i2$1.NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputGroupSlotComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-input-group-slot]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (icon) {\n      <nz-icon [nzType]=\"icon\" />\n    }\n    <ng-container *nzStringTemplateOutlet=\"template\">{{ template }}</ng-container>\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[class.ant-input-group-addon]': `type === 'addon'`,\n        '[class.ant-input-prefix]': `type === 'prefix'`,\n        '[class.ant-input-suffix]': `type === 'suffix'`\n      },\n      imports: [NzIconModule, NzOutletModule]\n    }]\n  }], null, {\n    icon: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }]\n  });\n})();\nclass NzInputDirective {\n  renderer;\n  elementRef;\n  hostView;\n  directionality;\n  nzBorderless = false;\n  nzSize = 'default';\n  nzStepperless = true;\n  nzStatus = '';\n  get disabled() {\n    if (this.ngControl && this.ngControl.disabled !== null) {\n      return this.ngControl.disabled;\n    }\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  disabled$ = new Subject();\n  dir = 'ltr';\n  // status\n  prefixCls = 'ant-input';\n  status = '';\n  statusCls = {};\n  hasFeedback = false;\n  feedbackRef = null;\n  components = [];\n  ngControl = inject(NgControl, {\n    self: true,\n    optional: true\n  });\n  finalSize = computed(() => {\n    if (this.compactSize) {\n      return this.compactSize();\n    }\n    return this.size();\n  });\n  size = signal(this.nzSize);\n  compactSize = inject(NZ_SPACE_COMPACT_SIZE, {\n    optional: true\n  });\n  destroy$ = inject(NzDestroyService);\n  nzFormStatusService = inject(NzFormStatusService, {\n    optional: true\n  });\n  nzFormNoStatusService = inject(NzFormNoStatusService, {\n    optional: true\n  });\n  constructor(renderer, elementRef, hostView, directionality) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.hostView = hostView;\n    this.directionality = directionality;\n  }\n  ngOnInit() {\n    this.nzFormStatusService?.formStatusChanges.pipe(distinctUntilChanged((pre, cur) => {\n      return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n    }), takeUntil(this.destroy$)).subscribe(({\n      status,\n      hasFeedback\n    }) => {\n      this.setStatusStyles(status, hasFeedback);\n    });\n    if (this.ngControl) {\n      this.ngControl.statusChanges?.pipe(filter(() => this.ngControl.disabled !== null), takeUntil(this.destroy$)).subscribe(() => {\n        this.disabled$.next(this.ngControl.disabled);\n      });\n    }\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges({\n    disabled,\n    nzStatus,\n    nzSize\n  }) {\n    if (disabled) {\n      this.disabled$.next(this.disabled);\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n    if (nzSize) {\n      this.size.set(nzSize.currentValue);\n    }\n  }\n  setStatusStyles(status, hasFeedback) {\n    // set inner status\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.renderFeedbackIcon();\n    // render status if nzStatus is set\n    this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n    Object.keys(this.statusCls).forEach(status => {\n      if (this.statusCls[status]) {\n        this.renderer.addClass(this.elementRef.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.elementRef.nativeElement, status);\n      }\n    });\n  }\n  renderFeedbackIcon() {\n    if (!this.status || !this.hasFeedback || !!this.nzFormNoStatusService) {\n      // remove feedback\n      this.hostView.clear();\n      this.feedbackRef = null;\n      return;\n    }\n    this.feedbackRef = this.feedbackRef || this.hostView.createComponent(NzFormItemFeedbackIconComponent);\n    this.feedbackRef.location.nativeElement.classList.add('ant-input-suffix');\n    this.feedbackRef.instance.status = this.status;\n    this.feedbackRef.instance.updateIcon();\n  }\n  static ɵfac = function NzInputDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1$2.Directionality));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzInputDirective,\n    selectors: [[\"input\", \"nz-input\", \"\"], [\"textarea\", \"nz-input\", \"\"]],\n    hostAttrs: [1, \"ant-input\"],\n    hostVars: 13,\n    hostBindings: function NzInputDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n        i0.ɵɵclassProp(\"ant-input-disabled\", ctx.disabled)(\"ant-input-borderless\", ctx.nzBorderless)(\"ant-input-lg\", ctx.finalSize() === \"large\")(\"ant-input-sm\", ctx.finalSize() === \"small\")(\"ant-input-rtl\", ctx.dir === \"rtl\")(\"ant-input-stepperless\", ctx.nzStepperless);\n      }\n    },\n    inputs: {\n      nzBorderless: [2, \"nzBorderless\", \"nzBorderless\", booleanAttribute],\n      nzSize: \"nzSize\",\n      nzStepperless: [2, \"nzStepperless\", \"nzStepperless\", booleanAttribute],\n      nzStatus: \"nzStatus\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    exportAs: [\"nzInput\"],\n    features: [i0.ɵɵProvidersFeature([NzDestroyService, {\n      provide: NZ_SPACE_COMPACT_ITEM_TYPE,\n      useValue: 'input'\n    }]), i0.ɵɵHostDirectivesFeature([i2$2.NzSpaceCompactItemDirective]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'input[nz-input],textarea[nz-input]',\n      exportAs: 'nzInput',\n      host: {\n        class: 'ant-input',\n        '[class.ant-input-disabled]': 'disabled',\n        '[class.ant-input-borderless]': 'nzBorderless',\n        '[class.ant-input-lg]': `finalSize() === 'large'`,\n        '[class.ant-input-sm]': `finalSize() === 'small'`,\n        '[attr.disabled]': 'disabled || null',\n        '[class.ant-input-rtl]': `dir=== 'rtl'`,\n        '[class.ant-input-stepperless]': `nzStepperless`\n      },\n      hostDirectives: [NzSpaceCompactItemDirective],\n      providers: [NzDestroyService, {\n        provide: NZ_SPACE_COMPACT_ITEM_TYPE,\n        useValue: 'input'\n      }]\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i1$2.Directionality\n  }], {\n    nzBorderless: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzStepperless: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass NzInputGroupWhitSuffixOrPrefixDirective {\n  elementRef;\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n  static ɵfac = function NzInputGroupWhitSuffixOrPrefixDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputGroupWhitSuffixOrPrefixDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzInputGroupWhitSuffixOrPrefixDirective,\n    selectors: [[\"nz-input-group\", \"nzSuffix\", \"\"], [\"nz-input-group\", \"nzPrefix\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputGroupWhitSuffixOrPrefixDirective, [{\n    type: Directive,\n    args: [{\n      selector: `nz-input-group[nzSuffix], nz-input-group[nzPrefix]`\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\nclass NzInputGroupComponent {\n  focusMonitor;\n  elementRef;\n  renderer;\n  cdr;\n  directionality;\n  listOfNzInputDirective;\n  nzAddOnBeforeIcon = null;\n  nzAddOnAfterIcon = null;\n  nzPrefixIcon = null;\n  nzSuffixIcon = null;\n  nzAddOnBefore;\n  nzAddOnAfter;\n  nzPrefix;\n  nzStatus = '';\n  nzSuffix;\n  nzSize = 'default';\n  nzSearch = false;\n  /**\n   * @deprecated Will be removed in v20. Use `NzSpaceCompactComponent` instead.\n   */\n  nzCompact = false;\n  isLarge = false;\n  isSmall = false;\n  isAffix = false;\n  isAddOn = false;\n  isFeedback = false;\n  focused = false;\n  disabled = false;\n  dir = 'ltr';\n  // status\n  prefixCls = 'ant-input';\n  affixStatusCls = {};\n  groupStatusCls = {};\n  affixInGroupStatusCls = {};\n  status = '';\n  hasFeedback = false;\n  destroy$ = new Subject();\n  nzFormStatusService = inject(NzFormStatusService, {\n    optional: true\n  });\n  nzFormNoStatusService = inject(NzFormNoStatusService, {\n    optional: true\n  });\n  constructor(focusMonitor, elementRef, renderer, cdr, directionality) {\n    this.focusMonitor = focusMonitor;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.directionality = directionality;\n  }\n  updateChildrenInputSize() {\n    if (this.listOfNzInputDirective) {\n      this.listOfNzInputDirective.forEach(item => item['size'].set(this.nzSize));\n    }\n  }\n  ngOnInit() {\n    this.nzFormStatusService?.formStatusChanges.pipe(distinctUntilChanged((pre, cur) => {\n      return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n    }), takeUntil(this.destroy$)).subscribe(({\n      status,\n      hasFeedback\n    }) => {\n      this.setStatusStyles(status, hasFeedback);\n    });\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      this.focused = !!focusOrigin;\n      this.cdr.markForCheck();\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngAfterContentInit() {\n    this.updateChildrenInputSize();\n    const listOfInputChange$ = this.listOfNzInputDirective.changes.pipe(startWith(this.listOfNzInputDirective));\n    listOfInputChange$.pipe(switchMap(list => merge(...[listOfInputChange$, ...list.map(input => input.disabled$)])), mergeMap(() => listOfInputChange$), map(list => list.some(input => input.disabled)), takeUntil(this.destroy$)).subscribe(disabled => {\n      this.disabled = disabled;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSize,\n      nzSuffix,\n      nzPrefix,\n      nzPrefixIcon,\n      nzSuffixIcon,\n      nzAddOnAfter,\n      nzAddOnBefore,\n      nzAddOnAfterIcon,\n      nzAddOnBeforeIcon,\n      nzStatus\n    } = changes;\n    if (nzSize) {\n      this.updateChildrenInputSize();\n      this.isLarge = this.nzSize === 'large';\n      this.isSmall = this.nzSize === 'small';\n    }\n    if (nzSuffix || nzPrefix || nzPrefixIcon || nzSuffixIcon) {\n      this.isAffix = !!(this.nzSuffix || this.nzPrefix || this.nzPrefixIcon || this.nzSuffixIcon);\n    }\n    if (nzAddOnAfter || nzAddOnBefore || nzAddOnAfterIcon || nzAddOnBeforeIcon) {\n      this.isAddOn = !!(this.nzAddOnAfter || this.nzAddOnBefore || this.nzAddOnAfterIcon || this.nzAddOnBeforeIcon);\n      this.nzFormNoStatusService?.noFormStatus?.next(this.isAddOn);\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setStatusStyles(status, hasFeedback) {\n    // set inner status\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.isFeedback = !!status && hasFeedback;\n    const baseAffix = !!(this.nzSuffix || this.nzPrefix || this.nzPrefixIcon || this.nzSuffixIcon);\n    this.isAffix = baseAffix || !this.isAddOn && hasFeedback;\n    this.affixInGroupStatusCls = this.isAffix || this.isFeedback ? this.affixStatusCls = getStatusClassNames(`${this.prefixCls}-affix-wrapper`, status, hasFeedback) : {};\n    this.cdr.markForCheck();\n    // render status if nzStatus is set\n    this.affixStatusCls = getStatusClassNames(`${this.prefixCls}-affix-wrapper`, this.isAddOn ? '' : status, this.isAddOn ? false : hasFeedback);\n    this.groupStatusCls = getStatusClassNames(`${this.prefixCls}-group-wrapper`, this.isAddOn ? status : '', this.isAddOn ? hasFeedback : false);\n    const statusCls = {\n      ...this.affixStatusCls,\n      ...this.groupStatusCls\n    };\n    Object.keys(statusCls).forEach(status => {\n      if (statusCls[status]) {\n        this.renderer.addClass(this.elementRef.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.elementRef.nativeElement, status);\n      }\n    });\n  }\n  static ɵfac = function NzInputGroupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputGroupComponent)(i0.ɵɵdirectiveInject(i1$3.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$2.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzInputGroupComponent,\n    selectors: [[\"nz-input-group\"]],\n    contentQueries: function NzInputGroupComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzInputDirective, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzInputDirective = _t);\n      }\n    },\n    hostVars: 40,\n    hostBindings: function NzInputGroupComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-input-group-compact\", ctx.nzCompact)(\"ant-input-search-enter-button\", ctx.nzSearch)(\"ant-input-search\", ctx.nzSearch)(\"ant-input-search-rtl\", ctx.dir === \"rtl\")(\"ant-input-search-sm\", ctx.nzSearch && ctx.isSmall)(\"ant-input-search-large\", ctx.nzSearch && ctx.isLarge)(\"ant-input-group-wrapper\", ctx.isAddOn)(\"ant-input-group-wrapper-rtl\", ctx.dir === \"rtl\")(\"ant-input-group-wrapper-lg\", ctx.isAddOn && ctx.isLarge)(\"ant-input-group-wrapper-sm\", ctx.isAddOn && ctx.isSmall)(\"ant-input-affix-wrapper\", ctx.isAffix && !ctx.isAddOn)(\"ant-input-affix-wrapper-rtl\", ctx.dir === \"rtl\")(\"ant-input-affix-wrapper-focused\", ctx.isAffix && ctx.focused)(\"ant-input-affix-wrapper-disabled\", ctx.isAffix && ctx.disabled)(\"ant-input-affix-wrapper-lg\", ctx.isAffix && !ctx.isAddOn && ctx.isLarge)(\"ant-input-affix-wrapper-sm\", ctx.isAffix && !ctx.isAddOn && ctx.isSmall)(\"ant-input-group\", !ctx.isAffix && !ctx.isAddOn)(\"ant-input-group-rtl\", ctx.dir === \"rtl\")(\"ant-input-group-lg\", !ctx.isAffix && !ctx.isAddOn && ctx.isLarge)(\"ant-input-group-sm\", !ctx.isAffix && !ctx.isAddOn && ctx.isSmall);\n      }\n    },\n    inputs: {\n      nzAddOnBeforeIcon: \"nzAddOnBeforeIcon\",\n      nzAddOnAfterIcon: \"nzAddOnAfterIcon\",\n      nzPrefixIcon: \"nzPrefixIcon\",\n      nzSuffixIcon: \"nzSuffixIcon\",\n      nzAddOnBefore: \"nzAddOnBefore\",\n      nzAddOnAfter: \"nzAddOnAfter\",\n      nzPrefix: \"nzPrefix\",\n      nzStatus: \"nzStatus\",\n      nzSuffix: \"nzSuffix\",\n      nzSize: \"nzSize\",\n      nzSearch: [2, \"nzSearch\", \"nzSearch\", booleanAttribute],\n      nzCompact: [2, \"nzCompact\", \"nzCompact\", booleanAttribute]\n    },\n    exportAs: [\"nzInputGroup\"],\n    features: [i0.ɵɵProvidersFeature([NzFormNoStatusService, {\n      provide: NZ_SPACE_COMPACT_ITEM_TYPE,\n      useValue: 'input'\n    }]), i0.ɵɵHostDirectivesFeature([i2$2.NzSpaceCompactItemDirective]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c1,\n    decls: 6,\n    vars: 1,\n    consts: [[\"affixTemplate\", \"\"], [\"contentTemplate\", \"\"], [1, \"ant-input-wrapper\", \"ant-input-group\"], [\"nz-input-group-slot\", \"\", \"type\", \"addon\", 3, \"icon\", \"template\"], [1, \"ant-input-affix-wrapper\", 3, \"ant-input-affix-wrapper-disabled\", \"ant-input-affix-wrapper-sm\", \"ant-input-affix-wrapper-lg\", \"ant-input-affix-wrapper-focused\", \"class\"], [3, \"ngTemplateOutlet\"], [1, \"ant-input-affix-wrapper\"], [\"nz-input-group-slot\", \"\", \"type\", \"prefix\", 3, \"icon\", \"template\"], [\"nz-input-group-slot\", \"\", \"type\", \"suffix\", 3, \"icon\", \"template\"], [3, \"status\"], [\"nz-input-group-slot\", \"\", \"type\", \"suffix\"]],\n    template: function NzInputGroupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_0_Template, 5, 3, \"span\", 2)(1, NzInputGroupComponent_Conditional_1_Template, 2, 1)(2, NzInputGroupComponent_ng_template_2_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzInputGroupComponent_ng_template_4_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.isAddOn ? 0 : 1);\n      }\n    },\n    dependencies: [NzInputGroupSlotComponent, NgTemplateOutlet, NzFormItemFeedbackIconComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-input-group',\n      exportAs: 'nzInputGroup',\n      imports: [NzInputGroupSlotComponent, NgTemplateOutlet, NzFormItemFeedbackIconComponent],\n      encapsulation: ViewEncapsulation.None,\n      providers: [NzFormNoStatusService, {\n        provide: NZ_SPACE_COMPACT_ITEM_TYPE,\n        useValue: 'input'\n      }],\n      template: `\n    @if (isAddOn) {\n      <span class=\"ant-input-wrapper ant-input-group\">\n        @if (nzAddOnBefore || nzAddOnBeforeIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnBeforeIcon\" [template]=\"nzAddOnBefore\"></span>\n        }\n\n        @if (isAffix || hasFeedback) {\n          <span\n            class=\"ant-input-affix-wrapper\"\n            [class.ant-input-affix-wrapper-disabled]=\"disabled\"\n            [class.ant-input-affix-wrapper-sm]=\"isSmall\"\n            [class.ant-input-affix-wrapper-lg]=\"isLarge\"\n            [class.ant-input-affix-wrapper-focused]=\"focused\"\n            [class]=\"affixInGroupStatusCls\"\n          >\n            <ng-template [ngTemplateOutlet]=\"affixTemplate\"></ng-template>\n          </span>\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n        }\n        @if (nzAddOnAfter || nzAddOnAfterIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnAfterIcon\" [template]=\"nzAddOnAfter\"></span>\n        }\n      </span>\n    } @else {\n      @if (isAffix) {\n        <ng-template [ngTemplateOutlet]=\"affixTemplate\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      }\n    }\n\n    <!-- affix template -->\n    <ng-template #affixTemplate>\n      @if (nzPrefix || nzPrefixIcon) {\n        <span nz-input-group-slot type=\"prefix\" [icon]=\"nzPrefixIcon\" [template]=\"nzPrefix\"></span>\n      }\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      @if (nzSuffix || nzSuffixIcon || isFeedback) {\n        <span nz-input-group-slot type=\"suffix\" [icon]=\"nzSuffixIcon\" [template]=\"nzSuffix\">\n          @if (isFeedback) {\n            <nz-form-item-feedback-icon [status]=\"status\" />\n          }\n        </span>\n      }\n    </ng-template>\n\n    <!-- content template -->\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n      @if (!isAddOn && !isAffix && isFeedback) {\n        <span nz-input-group-slot type=\"suffix\">\n          <nz-form-item-feedback-icon [status]=\"status\" />\n        </span>\n      }\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-input-group-compact]': `nzCompact`,\n        '[class.ant-input-search-enter-button]': `nzSearch`,\n        '[class.ant-input-search]': `nzSearch`,\n        '[class.ant-input-search-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-search-sm]': `nzSearch && isSmall`,\n        '[class.ant-input-search-large]': `nzSearch && isLarge`,\n        '[class.ant-input-group-wrapper]': `isAddOn`,\n        '[class.ant-input-group-wrapper-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-group-wrapper-lg]': `isAddOn && isLarge`,\n        '[class.ant-input-group-wrapper-sm]': `isAddOn && isSmall`,\n        '[class.ant-input-affix-wrapper]': `isAffix && !isAddOn`,\n        '[class.ant-input-affix-wrapper-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-affix-wrapper-focused]': `isAffix && focused`,\n        '[class.ant-input-affix-wrapper-disabled]': `isAffix && disabled`,\n        '[class.ant-input-affix-wrapper-lg]': `isAffix && !isAddOn && isLarge`,\n        '[class.ant-input-affix-wrapper-sm]': `isAffix && !isAddOn && isSmall`,\n        '[class.ant-input-group]': `!isAffix && !isAddOn`,\n        '[class.ant-input-group-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-group-lg]': `!isAffix && !isAddOn && isLarge`,\n        '[class.ant-input-group-sm]': `!isAffix && !isAddOn && isSmall`\n      },\n      hostDirectives: [NzSpaceCompactItemDirective],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: i1$3.FocusMonitor\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$2.Directionality\n  }], {\n    listOfNzInputDirective: [{\n      type: ContentChildren,\n      args: [NzInputDirective]\n    }],\n    nzAddOnBeforeIcon: [{\n      type: Input\n    }],\n    nzAddOnAfterIcon: [{\n      type: Input\n    }],\n    nzPrefixIcon: [{\n      type: Input\n    }],\n    nzSuffixIcon: [{\n      type: Input\n    }],\n    nzAddOnBefore: [{\n      type: Input\n    }],\n    nzAddOnAfter: [{\n      type: Input\n    }],\n    nzPrefix: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzSuffix: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzSearch: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzCompact: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputOtpComponent {\n  formBuilder;\n  nzDestroyService;\n  otpInputs;\n  nzLength = 6;\n  nzSize = 'default';\n  disabled = false;\n  nzStatus = '';\n  nzFormatter = value => value;\n  nzMask = null;\n  otpArray;\n  internalValue = [];\n  onChangeCallback;\n  onTouched = () => {};\n  constructor(formBuilder, nzDestroyService) {\n    this.formBuilder = formBuilder;\n    this.nzDestroyService = nzDestroyService;\n    this.createFormArray();\n  }\n  ngOnChanges(changes) {\n    if (changes['nzLength']?.currentValue) {\n      this.createFormArray();\n    }\n    if (changes['disabled']) {\n      this.setDisabledState(this.disabled);\n    }\n  }\n  onInput(index, event) {\n    const inputElement = event.target;\n    const nextInput = this.otpInputs.toArray()[index + 1];\n    if (inputElement.value && nextInput) {\n      nextInput.nativeElement.focus();\n    } else if (!nextInput) {\n      this.selectInputBox(index);\n    }\n  }\n  onFocus(event) {\n    const inputElement = event.target;\n    inputElement.select();\n  }\n  onKeyDown(index, event) {\n    const previousInput = this.otpInputs.toArray()[index - 1];\n    if (event.keyCode === BACKSPACE) {\n      event.preventDefault();\n      this.internalValue[index] = '';\n      this.otpArray.at(index).setValue('', {\n        emitEvent: false\n      });\n      if (previousInput) {\n        this.selectInputBox(index - 1);\n      }\n      this.emitValue();\n    }\n  }\n  writeValue(value) {\n    if (!value) {\n      this.otpArray.reset();\n      return;\n    }\n    const controlValues = value.split('');\n    this.internalValue = controlValues;\n    controlValues.forEach((val, i) => {\n      const formattedValue = this.nzFormatter(val);\n      const value = this.nzMask ? this.nzMask : formattedValue;\n      this.otpArray.at(i).setValue(value, {\n        emitEvent: false\n      });\n    });\n  }\n  registerOnChange(fn) {\n    this.onChangeCallback = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    if (isDisabled) {\n      this.otpArray.disable();\n    } else {\n      this.otpArray.enable();\n    }\n  }\n  onPaste(index, event) {\n    const pastedText = event.clipboardData?.getData('text') || '';\n    if (!pastedText) return;\n    let currentIndex = index;\n    for (const char of pastedText.split('')) {\n      if (currentIndex < this.nzLength) {\n        const formattedChar = this.nzFormatter(char);\n        this.internalValue[currentIndex] = char;\n        const maskedValue = this.nzMask ? this.nzMask : formattedChar;\n        this.otpArray.at(currentIndex).setValue(maskedValue, {\n          emitEvent: false\n        });\n        currentIndex++;\n      } else {\n        break;\n      }\n    }\n    event.preventDefault(); // this line is needed, otherwise the last index that is going to be selected will also be filled (in the next line).\n    this.selectInputBox(currentIndex);\n    this.emitValue();\n  }\n  createFormArray() {\n    this.otpArray = this.formBuilder.array([]);\n    this.internalValue = new Array(this.nzLength).fill('');\n    for (let i = 0; i < this.nzLength; i++) {\n      const control = this.formBuilder.nonNullable.control('', [Validators.required]);\n      control.valueChanges.pipe(tap(value => {\n        const unmaskedValue = this.nzFormatter(value);\n        this.internalValue[i] = unmaskedValue;\n        control.setValue(this.nzMask ?? unmaskedValue, {\n          emitEvent: false\n        });\n        this.emitValue();\n      }), takeUntil(this.nzDestroyService)).subscribe();\n      this.otpArray.push(control);\n    }\n  }\n  emitValue() {\n    const result = this.internalValue.join('');\n    if (this.onChangeCallback) {\n      this.onChangeCallback(result);\n    }\n  }\n  selectInputBox(index) {\n    const otpInputArray = this.otpInputs.toArray();\n    if (index >= otpInputArray.length) index = otpInputArray.length - 1;\n    otpInputArray[index].nativeElement.select();\n  }\n  static ɵfac = function NzInputOtpComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputOtpComponent)(i0.ɵɵdirectiveInject(i1$4.FormBuilder), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzInputOtpComponent,\n    selectors: [[\"nz-input-otp\"]],\n    viewQuery: function NzInputOtpComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.otpInputs = _t);\n      }\n    },\n    hostAttrs: [1, \"ant-otp\"],\n    inputs: {\n      nzLength: [2, \"nzLength\", \"nzLength\", numberAttribute],\n      nzSize: \"nzSize\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      nzStatus: \"nzStatus\",\n      nzFormatter: \"nzFormatter\",\n      nzMask: \"nzMask\"\n    },\n    exportAs: [\"nzInputOtp\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzInputOtpComponent),\n      multi: true\n    }, NzDestroyService]), i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 0,\n    consts: [[\"otpInput\", \"\"], [\"nz-input\", \"\", \"type\", \"text\", \"maxlength\", \"1\", \"size\", \"1\", 1, \"ant-otp-input\", 3, \"nzSize\", \"formControl\", \"nzStatus\"], [\"nz-input\", \"\", \"type\", \"text\", \"maxlength\", \"1\", \"size\", \"1\", 1, \"ant-otp-input\", 3, \"input\", \"focus\", \"keydown\", \"paste\", \"nzSize\", \"formControl\", \"nzStatus\"]],\n    template: function NzInputOtpComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵrepeaterCreate(0, NzInputOtpComponent_For_1_Template, 2, 3, \"input\", 1, i0.ɵɵrepeaterTrackByIndex);\n      }\n      if (rf & 2) {\n        i0.ɵɵrepeater(ctx.otpArray.controls);\n      }\n    },\n    dependencies: [NzInputDirective, ReactiveFormsModule, i1$4.DefaultValueAccessor, i1$4.NgControlStatus, i1$4.MaxLengthValidator, i1$4.FormControlDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputOtpComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-input-otp',\n      exportAs: 'nzInputOtp',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @for (item of otpArray.controls; track $index) {\n      <input\n        nz-input\n        class=\"ant-otp-input\"\n        type=\"text\"\n        maxlength=\"1\"\n        size=\"1\"\n        [nzSize]=\"nzSize\"\n        [formControl]=\"item\"\n        [nzStatus]=\"nzStatus\"\n        (input)=\"onInput($index, $event)\"\n        (focus)=\"onFocus($event)\"\n        (keydown)=\"onKeyDown($index, $event)\"\n        (paste)=\"onPaste($index, $event)\"\n        #otpInput\n      />\n    }\n  `,\n      host: {\n        class: 'ant-otp'\n      },\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzInputOtpComponent),\n        multi: true\n      }, NzDestroyService],\n      imports: [NzInputDirective, ReactiveFormsModule]\n    }]\n  }], () => [{\n    type: i1$4.FormBuilder\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    otpInputs: [{\n      type: ViewChildren,\n      args: ['otpInput']\n    }],\n    nzLength: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzFormatter: [{\n      type: Input\n    }],\n    nzMask: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTextareaCountComponent {\n  renderer;\n  elementRef;\n  nzInputDirective;\n  nzMaxCharacterCount = 0;\n  nzComputeCharacterCount = v => v.length;\n  nzFormatter = (c, m) => `${c}${m > 0 ? `/${m}` : ``}`;\n  configChange$ = new Subject();\n  destroy$ = new Subject();\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n  }\n  ngAfterContentInit() {\n    if (!this.nzInputDirective && isDevMode()) {\n      throw new Error('[nz-textarea-count]: Could not find matching textarea[nz-input] child.');\n    }\n    if (this.nzInputDirective.ngControl) {\n      const valueChanges = this.nzInputDirective.ngControl.valueChanges || EMPTY;\n      merge(valueChanges, this.configChange$).pipe(takeUntil(this.destroy$), map(() => this.nzInputDirective.ngControl.value), startWith(this.nzInputDirective.ngControl.value)).subscribe(value => {\n        this.setDataCount(value);\n      });\n    }\n  }\n  setDataCount(value) {\n    const inputValue = isNotNil(value) ? String(value) : '';\n    const currentCount = this.nzComputeCharacterCount(inputValue);\n    const dataCount = this.nzFormatter(currentCount, this.nzMaxCharacterCount);\n    this.renderer.setAttribute(this.elementRef.nativeElement, 'data-count', dataCount);\n  }\n  ngOnDestroy() {\n    this.configChange$.complete();\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTextareaCountComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTextareaCountComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTextareaCountComponent,\n    selectors: [[\"nz-textarea-count\"]],\n    contentQueries: function NzTextareaCountComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzInputDirective, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzInputDirective = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-input-textarea-show-count\"],\n    inputs: {\n      nzMaxCharacterCount: [2, \"nzMaxCharacterCount\", \"nzMaxCharacterCount\", numberAttribute],\n      nzComputeCharacterCount: \"nzComputeCharacterCount\",\n      nzFormatter: \"nzFormatter\"\n    },\n    ngContentSelectors: _c4,\n    decls: 1,\n    vars: 0,\n    template: function NzTextareaCountComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTextareaCountComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-textarea-count',\n      template: ` <ng-content select=\"textarea[nz-input]\"></ng-content> `,\n      host: {\n        class: 'ant-input-textarea-show-count'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzInputDirective: [{\n      type: ContentChild,\n      args: [NzInputDirective, {\n        static: true\n      }]\n    }],\n    nzMaxCharacterCount: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzComputeCharacterCount: [{\n      type: Input\n    }],\n    nzFormatter: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputModule {\n  static ɵfac = function NzInputModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzInputModule,\n    imports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupSlotComponent, NzInputGroupWhitSuffixOrPrefixDirective, NzInputOtpComponent],\n    exports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupWhitSuffixOrPrefixDirective, NzInputOtpComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzInputGroupComponent, NzInputGroupSlotComponent, NzInputOtpComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupSlotComponent, NzInputGroupWhitSuffixOrPrefixDirective, NzInputOtpComponent],\n      exports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupWhitSuffixOrPrefixDirective, NzInputOtpComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzAutosizeDirective, NzInputAddonAfterDirective, NzInputAddonBeforeDirective, NzInputDirective, NzInputGroupComponent, NzInputGroupSlotComponent, NzInputGroupWhitSuffixOrPrefixDirective, NzInputModule, NzInputOtpComponent, NzInputPrefixDirective, NzInputSuffixDirective, NzTextareaCountComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,QAAQ;AAAA,EACzC;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,oBAAoB,IAAI,cAAc,CAAC;AAAA,EACvC,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,eAAe,IAAI,gBAAgB,KAAK;AAAA,EACxC,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,cAAc;AAAA,EAClB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC;AAAA,EACA,SAAS;AAAA,EACT,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AAAA,EACX,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,aAAa;AACX,SAAK,WAAW,KAAK,SAAS,YAAY,KAAK,MAAM,IAAI;AACzD,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,wCAAwC,mBAAmB;AAChF,WAAO,KAAK,qBAAqB,kCAAoC,kBAAqB,iBAAiB,CAAC;AAAA,EAC9G;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,IAC1C,WAAW,CAAC,GAAG,6BAA6B;AAAA,IAC5C,UAAU;AAAA,IACV,cAAc,SAAS,6CAA6C,IAAI,KAAK;AAC3E,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qCAAqC,IAAI,WAAW,OAAO,EAAE,uCAAuC,IAAI,WAAW,SAAS,EAAE,uCAAuC,IAAI,WAAW,SAAS,EAAE,0CAA0C,IAAI,WAAW,YAAY;AAAA,MACrR;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;AAAA,IACtB,UAAU,SAAS,yCAAyC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,WAAW,CAAC;AAAA,MAC7F;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AAAA,MACxC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,eAAe;AAAA,IAC/C,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,YAAY;AAAA,MACtB,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6CAA6C;AAAA,QAC7C,+CAA+C;AAAA,QAC/C,+CAA+C;AAAA,QAC/C,kDAAkD;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AASH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,+BAA+B;AAAA,IACzC,SAAS,CAAC,+BAA+B;AAAA,EAC3C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,+BAA+B;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,+BAA+B;AAAA,MACzC,SAAS,CAAC,+BAA+B;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7KH,SAAS,gCAAgC,OAAO;AAM9C,SAAO,MAAM,YAAY,KAAK,MAAM,WAAW;AACjD;AAEA,SAAS,iCAAiC,OAAO;AAC/C,QAAM,QAAQ,MAAM,WAAW,MAAM,QAAQ,CAAC,KAAK,MAAM,kBAAkB,MAAM,eAAe,CAAC;AAKjG,SAAO,CAAC,CAAC,SAAS,MAAM,eAAe,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY;AAC7I;;;ACAA,IAAM,kCAAkC,IAAI,eAAe,qCAAqC;AAiBhG,IAAM,0CAA0C;AAAA,EAC9C,YAAY,CAAC,KAAK,SAAS,UAAU,MAAM,KAAK;AAClD;AAQA,IAAM,kBAAkB;AAKxB,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,SAAS;AACX;AAeA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAAA;AAAA,EAEpB,YAAY,IAAI,gBAAgB,IAAI;AAAA;AAAA,EAEpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,aAAa,WAAS;AAGpB,QAAI,KAAK,UAAU,YAAY,KAAK,aAAW,YAAY,MAAM,OAAO,GAAG;AACzE;AAAA,IACF;AACA,SAAK,UAAU,KAAK,UAAU;AAC9B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,WAAS;AAItB,QAAI,KAAK,IAAI,IAAI,KAAK,eAAe,iBAAiB;AACpD;AAAA,IACF;AAGA,SAAK,UAAU,KAAK,gCAAgC,KAAK,IAAI,aAAa,OAAO;AACjF,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,WAAS;AAGvB,QAAI,iCAAiC,KAAK,GAAG;AAC3C,WAAK,UAAU,KAAK,UAAU;AAC9B;AAAA,IACF;AAGA,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAMA,YAAW,OAAO,QAAQ;AAChC,UAAM,UAAU,OAAO,iCAAiC;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,kCACX,0CACA;AAGL,SAAK,mBAAmB,KAAK,UAAU,KAAK,KAAK,CAAC,CAAC;AACnD,SAAK,kBAAkB,KAAK,iBAAiB,KAAK,qBAAqB,CAAC;AAGxE,QAAI,KAAK,UAAU,WAAW;AAC5B,YAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,WAAK,oBAAoB,OAAO,kBAAkB,MAAM;AACtD,eAAO,CAAC,sBAAsB,UAAUA,WAAU,WAAW,KAAK,YAAY,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,aAAa,KAAK,cAAc,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,cAAc,KAAK,eAAe,4BAA4B,CAAC;AAAA,MACjU,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,SAAS;AACxB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,IAC/B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,4BAA2B;AAMpC,EAAAA,2BAA0BA,2BAA0B,WAAW,IAAI,CAAC,IAAI;AAKxE,EAAAA,2BAA0BA,2BAA0B,UAAU,IAAI,CAAC,IAAI;AACzE,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAEhE,IAAM,gCAAgC,IAAI,eAAe,mCAAmC;AAK5F,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,yBAAyB,OAAO,qBAAqB;AAAA;AAAA,EAErD,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAAA;AAAA,EAE9B,eAAe,oBAAI,IAAI;AAAA;AAAA,EAEvB,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,8BAA8B,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,MAAM;AAG3B,SAAK,iBAAiB;AACtB,SAAK,wBAAwB,WAAW,MAAM,KAAK,iBAAiB,KAAK;AAAA,EAC3E;AAAA;AAAA,EAEA,YAAY,OAAO,UAAU;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,6BAA6B,IAAI,QAAQ;AAAA,EACzC,cAAc;AACZ,UAAM,UAAU,OAAO,+BAA+B;AAAA,MACpD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,iBAAiB,SAAS,iBAAiB,0BAA0B;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC,WAAS;AACvC,UAAM,SAAS,gBAAgB,KAAK;AAEpC,aAAS,UAAU,QAAQ,SAAS,UAAU,QAAQ,eAAe;AACnE,UAAI,MAAM,SAAS,SAAS;AAC1B,aAAK,SAAS,OAAO,OAAO;AAAA,MAC9B,OAAO;AACL,aAAK,QAAQ,OAAO,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,gBAAgB,OAAO;AACtC,UAAM,gBAAgB,cAAc,OAAO;AAE3C,QAAI,CAAC,KAAK,UAAU,aAAa,cAAc,aAAa,GAAG;AAE7D,aAAO,GAAG;AAAA,IACZ;AAIA,UAAM,WAAW,eAAe,aAAa,KAAK,KAAK,aAAa;AACpE,UAAM,aAAa,KAAK,aAAa,IAAI,aAAa;AAEtD,QAAI,YAAY;AACd,UAAI,eAAe;AAIjB,mBAAW,gBAAgB;AAAA,MAC7B;AACA,aAAO,WAAW;AAAA,IACpB;AAEA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,SAAS,IAAI,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,SAAK,aAAa,IAAI,eAAe,IAAI;AACzC,SAAK,yBAAyB,IAAI;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,SAAS;AACtB,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,cAAc,KAAK,aAAa,IAAI,aAAa;AACvD,QAAI,aAAa;AACf,kBAAY,QAAQ,SAAS;AAC7B,WAAK,YAAY,aAAa;AAC9B,WAAK,aAAa,OAAO,aAAa;AACtC,WAAK,uBAAuB,WAAW;AAAA,IACzC;AAAA,EACF;AAAA,EACA,SAAS,SAAS,QAAQ,SAAS;AACjC,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,iBAAiB,KAAK,aAAa,EAAE;AAI3C,QAAI,kBAAkB,gBAAgB;AACpC,WAAK,wBAAwB,aAAa,EAAE,QAAQ,CAAC,CAAC,gBAAgB,IAAI,MAAM,KAAK,eAAe,gBAAgB,QAAQ,IAAI,CAAC;AAAA,IACnI,OAAO;AACL,WAAK,WAAW,MAAM;AAEtB,UAAI,OAAO,cAAc,UAAU,YAAY;AAC7C,sBAAc,MAAM,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAC5E;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,MAAM,KAAK,aAAa;AAC9B,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA,EACA,gBAAgB,kBAAkB;AAChC,QAAI,KAAK,SAAS;AAGhB,UAAI,KAAK,6BAA6B;AACpC,eAAO,KAAK,2BAA2B,gBAAgB,IAAI,UAAU;AAAA,MACvE,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAUA,QAAI,KAAK,kBAAkB,KAAK,kBAAkB;AAChD,aAAO,KAAK;AAAA,IACd;AAKA,QAAI,oBAAoB,KAAK,iCAAiC,gBAAgB,GAAG;AAC/E,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,2BAA2B,kBAAkB;AAW3C,WAAO,KAAK,mBAAmB,0BAA0B,YAAY,CAAC,CAAC,kBAAkB,SAAS,KAAK,uBAAuB,iBAAiB;AAAA,EACjJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,QAAQ;AAC3B,YAAQ,UAAU,OAAO,eAAe,CAAC,CAAC,MAAM;AAChD,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,wBAAwB,WAAW,UAAU;AACtE,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,uBAAuB,WAAW,SAAS;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,QAAQ,oBAAoB,OAAO;AAC5C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,UAAU;AACf,WAAK,8BAA8B,WAAW,WAAW;AAMzD,UAAI,KAAK,mBAAmB,0BAA0B,WAAW;AAC/D,qBAAa,KAAK,gBAAgB;AAClC,cAAM,KAAK,KAAK,8BAA8B,kBAAkB;AAChE,aAAK,mBAAmB,WAAW,MAAM,KAAK,UAAU,MAAM,EAAE;AAAA,MAClE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO,SAAS;AAOvB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,UAAM,mBAAmB,gBAAgB,KAAK;AAC9C,QAAI,CAAC,eAAe,CAAC,YAAY,iBAAiB,YAAY,kBAAkB;AAC9E;AAAA,IACF;AACA,SAAK,eAAe,SAAS,KAAK,gBAAgB,gBAAgB,GAAG,WAAW;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO,SAAS;AAGtB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,QAAI,CAAC,eAAe,YAAY,iBAAiB,MAAM,yBAAyB,QAAQ,QAAQ,SAAS,MAAM,aAAa,GAAG;AAC7H;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY,aAAa,IAAI;AAAA,EACpC;AAAA,EACA,YAAY,MAAM,QAAQ;AACxB,QAAI,KAAK,QAAQ,UAAU,QAAQ;AACjC,WAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,yBAAyB,aAAa;AACpC,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B;AAAA,IACF;AACA,UAAM,WAAW,YAAY;AAC7B,UAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ,KAAK;AACjF,QAAI,CAAC,wBAAwB;AAC3B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,iBAAS,iBAAiB,SAAS,KAAK,+BAA+B,2BAA2B;AAClG,iBAAS,iBAAiB,QAAQ,KAAK,+BAA+B,2BAA2B;AAAA,MACnG,CAAC;AAAA,IACH;AACA,SAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAEzE,QAAI,EAAE,KAAK,2BAA2B,GAAG;AAGvC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAMC,UAAS,KAAK,WAAW;AAC/B,QAAAA,QAAO,iBAAiB,SAAS,KAAK,oBAAoB;AAAA,MAC5D,CAAC;AAED,WAAK,uBAAuB,iBAAiB,KAAK,UAAU,KAAK,0BAA0B,CAAC,EAAE,UAAU,cAAY;AAClH,aAAK;AAAA,UAAW;AAAA,UAAU;AAAA;AAAA,QAA4B;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB,aAAa;AAClC,UAAM,WAAW,YAAY;AAC7B,QAAI,KAAK,4BAA4B,IAAI,QAAQ,GAAG;AAClD,YAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ;AAC5E,UAAI,yBAAyB,GAAG;AAC9B,aAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAAA,MAC3E,OAAO;AACL,iBAAS,oBAAoB,SAAS,KAAK,+BAA+B,2BAA2B;AACrG,iBAAS,oBAAoB,QAAQ,KAAK,+BAA+B,2BAA2B;AACpG,aAAK,4BAA4B,OAAO,QAAQ;AAAA,MAClD;AAAA,IACF;AAEA,QAAI,CAAE,EAAE,KAAK,wBAAwB;AACnC,YAAMA,UAAS,KAAK,WAAW;AAC/B,MAAAA,QAAO,oBAAoB,SAAS,KAAK,oBAAoB;AAE7D,WAAK,2BAA2B,KAAK;AAErC,mBAAa,KAAK,qBAAqB;AACvC,mBAAa,KAAK,gBAAgB;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,SAAS,QAAQ,aAAa;AAC3C,SAAK,YAAY,SAAS,MAAM;AAChC,SAAK,YAAY,aAAa,MAAM;AACpC,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,SAAS;AAC/B,UAAM,UAAU,CAAC;AACjB,SAAK,aAAa,QAAQ,CAAC,MAAM,mBAAmB;AAClD,UAAI,mBAAmB,WAAW,KAAK,iBAAiB,eAAe,SAAS,OAAO,GAAG;AACxF,gBAAQ,KAAK,CAAC,gBAAgB,IAAI,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iCAAiC,kBAAkB;AACjD,UAAM;AAAA,MACJ,mBAAmB;AAAA,MACnB;AAAA,IACF,IAAI,KAAK;AAIT,QAAI,uBAAuB,WAAW,CAAC,oBAAoB,qBAAqB,oBAAoB,iBAAiB,aAAa,WAAW,iBAAiB,aAAa,cAAc,iBAAiB,UAAU;AAClN,aAAO;AAAA,IACT;AACA,UAAM,SAAS,iBAAiB;AAChC,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,gBAAgB,GAAG;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc,OAAO,UAAU;AAAA,EAC/B,gBAAgB,OAAO,YAAY;AAAA,EACnC;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB,IAAI,aAAa;AAAA,EAClC,cAAc;AAAA,EAAC;AAAA,EACf,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,uBAAuB,KAAK,cAAc,QAAQ,SAAS,QAAQ,aAAa,KAAK,QAAQ,aAAa,wBAAwB,CAAC,EAAE,UAAU,YAAU;AAC5J,WAAK,eAAe;AACpB,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,YAAY;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,GAAG,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,IAClF,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AC7nBH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,UAAU,CAAC,mBAAmB;AAAA,IAC9B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC5D,QAAQ,CAAC,oQAAoQ;AAAA,IAC7Q,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,oQAAoQ;AAAA,IAC/Q,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACzBH,SAAS,mBAAmB,QAAQ;AAElC,MAAI,OAAO,SAAS,mBAAmB,OAAO,kBAAkB,SAAS;AACvE,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,aAAa;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,WAAW,QAAQ,KAAK;AACjD,UAAI,EAAE,OAAO,WAAW,CAAC,aAAa,UAAU;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,aAAa,QAAQ,KAAK;AACnD,UAAI,EAAE,OAAO,aAAa,CAAC,aAAa,UAAU;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO,OAAO,qBAAqB,cAAc,OAAO,IAAI,iBAAiB,QAAQ;AAAA,EACvF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,yBAAwB;AAAA,IACjC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,2BAA2B,OAAO,uBAAuB;AAAA;AAAA,EAEzD,oBAAoB,oBAAI,IAAI;AAAA,EAC5B,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,cAAc;AACZ,SAAK,kBAAkB,QAAQ,CAAC,GAAG,YAAY,KAAK,iBAAiB,OAAO,CAAC;AAAA,EAC/E;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,SAAS,KAAK,gBAAgB,OAAO;AAC3C,YAAM,eAAe,OAAO,KAAK,IAAI,aAAW,QAAQ,OAAO,YAAU,CAAC,mBAAmB,MAAM,CAAC,CAAC,GAAG,OAAO,aAAW,CAAC,CAAC,QAAQ,MAAM,CAAC,EAAE,UAAU,aAAW;AAChK,aAAK,QAAQ,IAAI,MAAM;AACrB,mBAAS,KAAK,OAAO;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AACD,aAAO,MAAM;AACX,qBAAa,YAAY;AACzB,aAAK,kBAAkB,OAAO;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS;AACvB,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACxC,cAAM,SAAS,IAAI,QAAQ;AAC3B,cAAM,WAAW,KAAK,yBAAyB,OAAO,eAAa,OAAO,KAAK,SAAS,CAAC;AACzF,YAAI,UAAU;AACZ,mBAAS,QAAQ,SAAS;AAAA,YACxB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AACA,aAAK,kBAAkB,IAAI,SAAS;AAAA,UAClC;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,aAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,MACtC;AACA,aAAO,KAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS;AACzB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,WAAK,kBAAkB,IAAI,OAAO,EAAE;AACpC,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,EAAE,OAAO;AAC9C,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,kBAAkB,IAAI,OAAO;AACtC,UAAI,UAAU;AACZ,iBAAS,WAAW;AAAA,MACtB;AACA,aAAO,SAAS;AAChB,WAAK,kBAAkB,OAAO,OAAO;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,mBAAmB,OAAO,eAAe;AAAA,EACzC,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B,QAAQ,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,EACzD;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,qBAAqB,KAAK;AAC3C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,qBAAqB;AACnB,QAAI,CAAC,KAAK,wBAAwB,CAAC,KAAK,UAAU;AAChD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,aAAa;AAClB,UAAM,SAAS,KAAK,iBAAiB,QAAQ,KAAK,WAAW;AAC7D,SAAK,wBAAwB,KAAK,WAAW,OAAO,KAAK,aAAa,KAAK,QAAQ,CAAC,IAAI,QAAQ,UAAU,KAAK,KAAK;AAAA,EACtH;AAAA,EACA,eAAe;AACb,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,6BAA6B,YAAY,gBAAgB;AAAA,MACvE,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB;AAAA,IAC3B,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,uBAAuB;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,MAC3B,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3OH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,WAAW,SAAS;AAGlB,WAAO,QAAQ,aAAa,UAAU;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,SAAS;AACjB,WAAO,YAAY,OAAO,KAAK,iBAAiB,OAAO,EAAE,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAElB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,gBAAgB,UAAU,OAAO,CAAC;AACvD,QAAI,cAAc;AAEhB,UAAI,iBAAiB,YAAY,MAAM,IAAI;AACzC,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,UAAU,YAAY,GAAG;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,QAAI,gBAAgB,iBAAiB,OAAO;AAC5C,QAAI,QAAQ,aAAa,iBAAiB,GAAG;AAC3C,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,YAAY,aAAa,UAAU;AAIlD,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO,CAAC,yBAAyB,OAAO,GAAG;AACrF,aAAO;AAAA,IACT;AACA,QAAI,aAAa,SAAS;AAGxB,UAAI,CAAC,QAAQ,aAAa,UAAU,GAAG;AACrC,eAAO;AAAA,MACT;AAGA,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,SAAS;AAKxB,UAAI,kBAAkB,IAAI;AACxB,eAAO;AAAA,MACT;AAGA,UAAI,kBAAkB,MAAM;AAC1B,eAAO;AAAA,MACT;AAIA,aAAO,KAAK,UAAU,WAAW,QAAQ,aAAa,UAAU;AAAA,IAClE;AACA,WAAO,QAAQ,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,SAAS,QAAQ;AAG3B,WAAO,uBAAuB,OAAO,KAAK,CAAC,KAAK,WAAW,OAAO,MAAM,QAAQ,oBAAoB,KAAK,UAAU,OAAO;AAAA,EAC5H;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,SAAS,gBAAgBC,SAAQ;AAC/B,MAAI;AACF,WAAOA,QAAO;AAAA,EAChB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,SAAS;AAG5B,SAAO,CAAC,EAAE,QAAQ,eAAe,QAAQ,gBAAgB,OAAO,QAAQ,mBAAmB,cAAc,QAAQ,eAAe,EAAE;AACpI;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,SAAO,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY,aAAa;AAChG;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,eAAe,OAAO,KAAK,QAAQ,QAAQ;AACpD;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,gBAAgB,OAAO,KAAK,QAAQ,aAAa,MAAM;AAChE;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,QAAQ,aAAa,UAAU,KAAK,QAAQ,aAAa,QAAW;AACvE,WAAO;AAAA,EACT;AACA,MAAI,WAAW,QAAQ,aAAa,UAAU;AAC9C,SAAO,CAAC,EAAE,YAAY,CAAC,MAAM,SAAS,UAAU,EAAE,CAAC;AACrD;AAKA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,iBAAiB,OAAO,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,SAAS,QAAQ,aAAa,UAAU,KAAK,IAAI,EAAE;AACpE,SAAO,MAAM,QAAQ,IAAI,KAAK;AAChC;AAEA,SAAS,yBAAyB,SAAS;AACzC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,MAAI,YAAY,aAAa,WAAW,QAAQ;AAChD,SAAO,cAAc,UAAU,cAAc,cAAc,aAAa,YAAY,aAAa;AACnG;AAKA,SAAS,uBAAuB,SAAS;AAEvC,MAAI,cAAc,OAAO,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,oBAAoB,OAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ,aAAa,iBAAiB,KAAK,iBAAiB,OAAO;AACzI;AAEA,SAAS,UAAU,MAAM;AAEvB,SAAO,KAAK,iBAAiB,KAAK,cAAc,eAAe;AACjE;AASA,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA;AAAA,EAEf,sBAAsB,MAAM,KAAK,yBAAyB;AAAA,EAC1D,oBAAoB,MAAM,KAAK,0BAA0B;AAAA;AAAA,EAEzD,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,OAAO,KAAK,YAAY;AACnD,WAAK,sBAAsB,OAAO,KAAK,UAAU;AAAA,IACnD;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,YAAY,UAAU,UAAU,SAAS,WAAW,eAAe,OACnE,WAAW;AACT,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,QAAI,CAAC,cAAc;AACjB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,QAAI,aAAa;AACf,kBAAY,oBAAoB,SAAS,KAAK,mBAAmB;AACjE,kBAAY,OAAO;AAAA,IACrB;AACA,QAAI,WAAW;AACb,gBAAU,oBAAoB,SAAS,KAAK,iBAAiB;AAC7D,gBAAU,OAAO;AAAA,IACnB;AACA,SAAK,eAAe,KAAK,aAAa;AACtC,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AAEd,QAAI,KAAK,cAAc;AACrB,aAAO;AAAA,IACT;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,KAAK,cAAc;AACvC,aAAK,aAAa,iBAAiB,SAAS,KAAK,mBAAmB;AAAA,MACtE;AACA,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,KAAK,cAAc;AACrC,aAAK,WAAW,iBAAiB,SAAS,KAAK,iBAAiB;AAAA,MAClE;AAAA,IACF,CAAC;AACD,QAAI,KAAK,SAAS,YAAY;AAC5B,WAAK,SAAS,WAAW,aAAa,KAAK,cAAc,KAAK,QAAQ;AACtE,WAAK,SAAS,WAAW,aAAa,KAAK,YAAY,KAAK,SAAS,WAAW;AAChF,WAAK,eAAe;AAAA,IACtB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,SAAS;AACpC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,oBAAoB,OAAO,CAAC,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC,SAAS;AAC1C,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,0BAA0B,OAAO,CAAC,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kCAAkC,SAAS;AACzC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,yBAAyB,OAAO,CAAC,CAAC;AAAA,IAC7E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,OAAO;AAExB,UAAM,UAAU,KAAK,SAAS,iBAAiB,qBAAqB,KAAK,qBAA0B,KAAK,iBAAsB,KAAK,GAAG;AACtI,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAEvC,YAAI,QAAQ,CAAC,EAAE,aAAa,aAAa,KAAK,EAAE,GAAG;AACjD,kBAAQ,KAAK,gDAAgD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QACvL,WAAW,QAAQ,CAAC,EAAE,aAAa,oBAAoB,KAAK,EAAE,GAAG;AAC/D,kBAAQ,KAAK,uDAAuD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QAC9L;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,SAAS;AACpB,aAAO,QAAQ,SAAS,QAAQ,CAAC,IAAI,KAAK,yBAAyB,KAAK,QAAQ;AAAA,IAClF;AACA,WAAO,QAAQ,SAAS,QAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,wBAAwB,KAAK,QAAQ;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,SAAS;AAE3B,UAAM,oBAAoB,KAAK,SAAS,cAAc,wCAA6C;AACnG,QAAI,mBAAmB;AAErB,WAAK,OAAO,cAAc,eAAe,cAAc,kBAAkB,aAAa,mBAAmB,GAAG;AAC1G,gBAAQ,KAAK,2IAAqJ,iBAAiB;AAAA,MACrL;AAGA,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACpG,gBAAQ,KAAK,0DAA0D,iBAAiB;AAAA,MAC1F;AACA,UAAI,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACjD,cAAM,iBAAiB,KAAK,yBAAyB,iBAAiB;AACtE,wBAAgB,MAAM,OAAO;AAC7B,eAAO,CAAC,CAAC;AAAA,MACX;AACA,wBAAkB,MAAM,OAAO;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,SAAS;AACjC,UAAM,oBAAoB,KAAK,mBAAmB,OAAO;AACzD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,SAAS;AAChC,UAAM,oBAAoB,KAAK,mBAAmB,KAAK;AACvD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,yBAAyB,MAAM;AAC7B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,yBAAyB,SAAS,CAAC,CAAC,IAAI;AAC1H,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB,MAAM;AAC5B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,wBAAwB,SAAS,CAAC,CAAC,IAAI;AACzH,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AACjD,SAAK,sBAAsB,KAAK,UAAU,MAAM;AAChD,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,UAAU,IAAI,uBAAuB;AAC5C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,WAAW,QAAQ;AAGvC,gBAAY,OAAO,aAAa,YAAY,GAAG,IAAI,OAAO,gBAAgB,UAAU;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS;AACrB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,SAAS,KAAK,YAAY;AACrD,WAAK,sBAAsB,SAAS,KAAK,UAAU;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,IAAI;AAEnB,QAAI,KAAK,WAAW;AAClB,sBAAgB,IAAI;AAAA,QAClB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,EAAE;AAAA,IACf;AAAA,EACF;AACF;AAIA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,uBAAuB,OAAO;AAC5C,WAAO,IAAI,UAAU,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,sBAAsB,KAAK,SAAS;AAAA,EACjH;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc,OAAO,UAAU;AAAA,EAC/B,oBAAoB,OAAO,gBAAgB;AAAA;AAAA,EAE3C;AAAA;AAAA,EAEA,4BAA4B;AAAA;AAAA,EAE5B,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,WAAW;AAAA,EACpC;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,OAAO,QAAQ;AAChC,QAAI,SAAS,WAAW;AACtB,WAAK,YAAY,KAAK,kBAAkB,OAAO,KAAK,YAAY,eAAe,IAAI;AAAA,IACrF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,QAAQ;AAGxB,QAAI,KAAK,2BAA2B;AAClC,WAAK,0BAA0B,MAAM;AACrC,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,cAAc;AAC9B,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,YAAY,GAAG;AACnD,WAAK,UAAU,cAAc;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,oBAAoB,QAAQ,aAAa;AAC/C,QAAI,qBAAqB,CAAC,kBAAkB,eAAe,KAAK,eAAe,KAAK,WAAW,YAAY,GAAG;AAC5G,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,kCAAkC;AACnE,SAAK,WAAW,6BAA6B;AAAA,EAC/C;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,gBAAgB,WAAW,gBAAgB;AAAA,MACxD,aAAa,CAAC,GAAG,2BAA2B,eAAe,gBAAgB;AAAA,IAC7E;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,+BAA+B,IAAI,eAAe,wBAAwB;AAAA,EAC9E,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uCAAuC;AAC9C,SAAO;AACT;AAEA,IAAM,iCAAiC,IAAI,eAAe,gCAAgC;AAC1F,IAAI,YAAY;AAChB,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO,MAAM;AAAA,EACvB,kBAAkB,OAAO,gCAAgC;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM,eAAe,OAAO,8BAA8B;AAAA,MACxD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe,gBAAgB,KAAK,mBAAmB;AAAA,EAC9D;AAAA,EACA,SAAS,YAAY,MAAM;AACzB,UAAM,iBAAiB,KAAK;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,UAAU;AACpD,iBAAW,KAAK,CAAC;AAAA,IACnB,OAAO;AACL,OAAC,YAAY,QAAQ,IAAI;AAAA,IAC3B;AACA,SAAK,MAAM;AACX,iBAAa,KAAK,gBAAgB;AAClC,QAAI,CAAC,YAAY;AACf,mBAAa,kBAAkB,eAAe,aAAa,eAAe,aAAa;AAAA,IACzF;AACA,QAAI,YAAY,QAAQ,gBAAgB;AACtC,iBAAW,eAAe;AAAA,IAC5B;AAEA,SAAK,aAAa,aAAa,aAAa,UAAU;AACtD,QAAI,KAAK,aAAa,IAAI;AACxB,WAAK,yBAAyB,KAAK,aAAa,EAAE;AAAA,IACpD;AAMA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAkB,IAAI,QAAQ,aAAW,KAAK,kBAAkB,OAAO;AAAA,MAC9E;AACA,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB,WAAW,MAAM;AACvC,aAAK,aAAa,cAAc;AAChC,YAAI,OAAO,aAAa,UAAU;AAChC,eAAK,mBAAmB,WAAW,MAAM,KAAK,MAAM,GAAG,QAAQ;AAAA,QACjE;AAGA,aAAK,kBAAkB;AACvB,aAAK,kBAAkB,KAAK,kBAAkB;AAAA,MAChD,GAAG,GAAG;AACN,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,cAAc;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,gBAAgB;AAClC,SAAK,cAAc,OAAO;AAC1B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK,kBAAkB;AAAA,EAChD;AAAA,EACA,qBAAqB;AACnB,UAAM,eAAe;AACrB,UAAM,mBAAmB,KAAK,UAAU,uBAAuB,YAAY;AAC3E,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AAEjD,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,WAAO,UAAU,IAAI,YAAY;AACjC,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO,aAAa,aAAa,QAAQ;AACzC,WAAO,KAAK,sBAAsB,WAAW;AAC7C,SAAK,UAAU,KAAK,YAAY,MAAM;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,IAAI;AAO3B,UAAM,SAAS,KAAK,UAAU,iBAAiB,mDAAmD;AAClG,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,UAAI,CAAC,UAAU;AACb,cAAM,aAAa,aAAa,EAAE;AAAA,MACpC,WAAW,SAAS,QAAQ,EAAE,MAAM,IAAI;AACtC,cAAM,aAAa,aAAa,WAAW,MAAM,EAAE;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,iBAAiB,OAAO,aAAa;AAAA,EACrC,mBAAmB,OAAO,eAAe;AAAA,EACzC,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc,UAAU,SAAS,UAAU,cAAc,QAAQ;AACtE,QAAI,KAAK,gBAAgB,OAAO;AAC9B,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,YAAY;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,WAAW,CAAC,KAAK,eAAe;AAC9B,WAAK,gBAAgB,KAAK,QAAQ,kBAAkB,MAAM;AACxD,eAAO,KAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,MAAM;AAErE,gBAAM,cAAc,KAAK,YAAY,cAAc;AAGnD,cAAI,gBAAgB,KAAK,wBAAwB;AAC/C,iBAAK,eAAe,SAAS,aAAa,KAAK,aAAa,KAAK,QAAQ;AACzE,iBAAK,yBAAyB;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AAAA;AAAA,EAEd;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,eAAe,YAAY;AAAA,MAC3C,UAAU,CAAC,GAAG,uBAAuB,UAAU;AAAA,IACjD;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AACjD,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC3D,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC7D,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,IAAM,2BAA2B;AAEjC,IAAM,2BAA2B;AAEjC,IAAM,sCAAsC;AAY5C,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,OAAO,kBAAkB,EAAE,QAAQ,yBAAyB,EAAE,UAAU,MAAM;AAC3G,UAAI,KAAK,6BAA6B;AACpC,aAAK,8BAA8B;AACnC,aAAK,qCAAqC;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO,iBAAiB;AAAA,IAC1B;AAIA,UAAM,cAAc,KAAK,UAAU,cAAc,KAAK;AACtD,gBAAY,MAAM,kBAAkB;AACpC,gBAAY,MAAM,WAAW;AAC7B,SAAK,UAAU,KAAK,YAAY,WAAW;AAK3C,UAAM,iBAAiB,KAAK,UAAU,eAAe;AACrD,UAAM,gBAAgB,kBAAkB,eAAe,mBAAmB,eAAe,iBAAiB,WAAW,IAAI;AACzH,UAAM,iBAAiB,iBAAiB,cAAc,mBAAmB,IAAI,QAAQ,MAAM,EAAE;AAC7F,gBAAY,OAAO;AACnB,YAAQ,eAAe;AAAA;AAAA,MAErB,KAAK;AAAA;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,iBAAiB;AAAA;AAAA,MAE1B,KAAK;AAAA;AAAA,MAEL,KAAK;AACH,eAAO,iBAAiB;AAAA,IAC5B;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,cAAc;AACZ,SAAK,wBAAwB,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,uCAAuC;AACrC,QAAI,CAAC,KAAK,+BAA+B,KAAK,UAAU,aAAa,KAAK,UAAU,MAAM;AACxF,YAAM,cAAc,KAAK,UAAU,KAAK;AACxC,kBAAY,OAAO,qCAAqC,0BAA0B,wBAAwB;AAC1G,WAAK,8BAA8B;AACnC,YAAM,OAAO,KAAK,oBAAoB;AACtC,UAAI,SAAS,iBAAiB,gBAAgB;AAC5C,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E,WAAW,SAAS,iBAAiB,gBAAgB;AACnD,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,0BAAyB;AAAA,IAClC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc;AACZ,WAAO,wBAAwB,EAAE,qCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,IACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,EACtD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,MACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;AC99BH,IAAM,yCAAyC;AAK/C,IAAM,YAAN,MAAgB;AAAA,EACd,mBAAmB,IAAI,QAAQ;AAAA,EAC/B,SAAS,CAAC;AAAA,EACV,qBAAqB;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA,EACnB;AAAA,EACA,gBAAgB,IAAI,QAAQ;AAAA,EAC5B,eAAe,KAAK;AAAA,EACpB,YAAY,cAAc,QAAQ;AAChC,UAAM,oBAAoB,OAAO,QAAQ,qBAAqB,WAAW,OAAO,mBAAmB;AACnG,QAAI,QAAQ,eAAe;AACzB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,SAAK,OAAO,cAAc,eAAe,cAAc,aAAa,UAAU,aAAa,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC5I,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC5F;AACA,SAAK,SAAS,YAAY;AAC1B,SAAK,iBAAiB,iBAAiB;AAAA,EACzC;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,CAAC;AACxB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AAGtB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG;AACvC,WAAK,iBAAiB,KAAK,MAAM,IAAI,kBAAkB,CAAC;AAAA,IAC1D,WAAW,WAAW,KAAK,WAAW,KAAK,WAAW,QAAQ,WAAW,MAAM;AAC7E,WAAK,iBAAiB,KAAK,OAAO,aAAa,OAAO,CAAC;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,gBAAgB,SAAS;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,iBAAiB,mBAAmB;AAIlC,SAAK,iBAAiB,KAAK,IAAI,YAAU,KAAK,gBAAgB,KAAK,MAAM,CAAC,GAAG,aAAa,iBAAiB,GAAG,OAAO,MAAM,KAAK,gBAAgB,SAAS,CAAC,GAAG,IAAI,MAAM,KAAK,gBAAgB,KAAK,EAAE,EAAE,kBAAkB,CAAC,CAAC,EAAE,UAAU,iBAAe;AAGlP,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK;AAC/C,cAAM,SAAS,KAAK,qBAAqB,KAAK,KAAK,OAAO;AAC1D,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,YAAI,CAAC,KAAK,mBAAmB,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,WAAW,MAAM,GAAG;AAC7G,eAAK,cAAc,KAAK,IAAI;AAC5B;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;;;ACrEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,CAAC,aAAa,IAAI,GAAG;AACvB,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;;;ACAA,IAAM,iBAAN,MAAqB;AAAA;AAAA,EAEnB,mBAAmB;AAAA;AAAA,EAEnB,cAAc;AAAA;AAAA,EAEd,+BAA+B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB,mBAAmB,WAAS;AAAA;AAAA,EAE5B,aAAa,UAAQ;AAAA;AAAA,EAErB,SAAS,CAAC;AAAA,EACV;AAAA,EACA,yBAAyB,aAAa;AAAA,EACtC,qBAAqB;AAAA,EACrB,mBAAmB;AACjB,QAAI,KAAK,sBAAsB,KAAK,OAAO,WAAW,GAAG;AACvD;AAAA,IACF;AACA,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,gBAAgB,KAAK,OAAO,CAAC,CAAC,GAAG;AACnF,sBAAc;AACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,WAAW;AAG1C,QAAI,WAAW,eAAe;AAC5B,WAAK,aAAa,QAAQ;AAC1B,WAAK,mBAAmB;AACxB,WAAK,cAAc;AACnB,WAAK,YAAY,4BAA4B,WAAW;AACxD,iBAAW,cAAc;AAAA,IAC3B,OAAO;AAEL,WAAK,UAAU,WAAW;AAAA,IAC5B;AACA,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,OAAO,QAAQ;AAIzB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,SAAS,MAAM,QAAQ;AAC5B,YAAM,QAAQ,UAAU,cAAY;AAClC,aAAK,SAAS,SAAS,QAAQ;AAC/B,aAAK,YAAY,SAAS,KAAK,MAAM;AACrC,aAAK,uBAAuB,KAAK,MAAM;AACvC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,WAAW,aAAa,KAAK,GAAG;AAC9B,YAAM,UAAU,cAAY;AAC1B,aAAK,SAAS;AACd,aAAK,YAAY,SAAS,QAAQ;AAClC,aAAK,uBAAuB,QAAQ;AACpC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AACd,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,OAAO,OAAO,gCAAgC,WAAW;AAC3D,WAAK,+BAA+B,OAAO;AAAA,IAC7C;AACA,QAAI,OAAO,uBAAuB;AAChC,WAAK,yBAAyB,OAAO;AAAA,IACvC;AACA,QAAI,OAAO,eAAe;AACxB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,QAAI,OAAO,SAAS;AAClB,WAAK,aAAa,OAAO;AAAA,IAC3B;AACA,QAAI,OAAO,OAAO,8BAA8B,aAAa;AAC3D,WAAK,cAAc,OAAO,yBAAyB;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,UAAU;AACR,SAAK,uBAAuB,YAAY;AACxC,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,MAAM,MAAM;AAClB,YAAQ,KAAK;AAAA,MACX,KAAK;AAEH;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,qBAAqB,IAAI,KAAK,mBAAmB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,mBAAmB,IAAI,KAAK,qBAAqB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB;AACrB;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,qBAAqB;AAC1B;AAAA,MACF;AACE,YAAI,MAAM,QAAQ,KAAK;AACrB,eAAK,kCAAkC;AACvC;AAAA,QACF;AACA,aAAK,YAAY,UAAU,KAAK;AAGhC;AAAA,IACJ;AAEA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,UAAU,KAAK,4BAA4B,EAAE,CAAC;AAAA,EACrD;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,gCAAgC,KAAK,OAAO,MAAM,CAAC;AAAA,EACzE;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,4BAA4B,KAAK,gBAAgB,CAAC;AAAA,EACxE;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,UAAU,KAAK,gCAAgC,KAAK,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EACA,UAAU,aAAa,UAAU,CAAC,GAAG;AAEnC,YAAQ,oBAAoB;AAC5B,QAAI,QAAQ,OAAO,gBAAgB,WAAW,cAAc,KAAK,OAAO,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,WAAW,CAAC;AAChJ,QAAI,QAAQ,KAAK,SAAS,KAAK,OAAO,QAAQ;AAC5C;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,KAAK;AAEpC,QAAI,KAAK,gBAAgB,QAAQ,KAAK,WAAW,UAAU,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG;AAClG;AAAA,IACF;AACA,UAAM,qBAAqB,KAAK;AAChC,SAAK,cAAc,cAAc;AACjC,SAAK,mBAAmB;AACxB,SAAK,YAAY,4BAA4B,KAAK;AAClD,SAAK,aAAa,MAAM;AACxB,wBAAoB,QAAQ;AAC5B,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,OAAO,KAAK,KAAK,WAAW;AAAA,IACnC;AACA,QAAI,KAAK,8BAA8B;AACrC,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,uBAAuB,UAAU;AAC/B,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,UAAM,WAAW,SAAS,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,UAAU,CAAC;AACjG,QAAI,WAAW,MAAM,aAAa,KAAK,kBAAkB;AACvD,WAAK,mBAAmB;AACxB,WAAK,YAAY,4BAA4B,QAAQ;AAAA,IACvD;AAAA,EACF;AAAA,EACA,cAAc,kBAAkB;AAC9B,SAAK,aAAa,IAAI,UAAU,KAAK,QAAQ;AAAA,MAC3C,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACnD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AAC3E,WAAK,UAAU,IAAI;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B,eAAe;AACzC,aAAS,IAAI,gBAAgB,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3D,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gCAAgC,eAAe;AAC7C,aAAS,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,KAAK,uBAAuB,GAAG;AACjC,WAAK,YAAY,SAAS;AAAA,IAC5B,OAAO;AACL,YAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,UAAI,CAAC,UAAU,KAAK,iBAAiB,MAAM,GAAG;AAC5C;AAAA,MACF;AACA,WAAK,UAAU,MAAM;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC,WAAK,YAAY,OAAO;AAAA,IAC1B,OAAO;AACL,uBAAiB,KAAK,YAAY,YAAY,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,cAAY;AACnF,cAAM,aAAa,SAAS,KAAK,WAAS,CAAC,KAAK,iBAAiB,KAAK,CAAC;AACvE,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AACA,aAAK,UAAU,UAAU;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,YAAY,eAAe,YAAY,KAAK,YAAY,aAAa,KAAK,YAAY,WAAW;AAAA,EACtH;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,OAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,aAAa;AAAA,EACpF;AAAA;AAAA,EAEA,oCAAoC;AAClC,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,QAAI;AACJ,QAAI,CAAC,QAAQ;AACX,sBAAgB,GAAG,KAAK,OAAO,OAAO,UAAQ,KAAK,UAAU,MAAM,IAAI,CAAC;AAAA,IAC1E,OAAO;AACL,sBAAgB,iBAAiB,OAAO,YAAY,CAAC;AAAA,IACvD;AACA,kBAAc,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAC7C,iBAAW,QAAQ,OAAO;AACxB,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,SAAK,aAAa,SAAS;AAAA,EAC7B;AACF;AAMA,SAAS,2BAA2B;AAClC,SAAO,CAAC,OAAO,YAAY,IAAI,eAAe,OAAO,OAAO;AAC9D;AAEA,IAAM,mBAAmB,IAAI,eAAe,oBAAoB;AAAA,EAC9D,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;;;AC3SD,IAAM,eAAe;AAKrB,SAAS,oBAAoB,IAAI,MAAM,IAAI;AACzC,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,MAAI,IAAI,KAAK,gBAAc,WAAW,KAAK,MAAM,EAAE,GAAG;AACpD;AAAA,EACF;AACA,MAAI,KAAK,EAAE;AACX,KAAG,aAAa,MAAM,IAAI,KAAK,YAAY,CAAC;AAC9C;AAKA,SAAS,uBAAuB,IAAI,MAAM,IAAI;AAC5C,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,QAAM,cAAc,IAAI,OAAO,SAAO,QAAQ,EAAE;AAChD,MAAI,YAAY,QAAQ;AACtB,OAAG,aAAa,MAAM,YAAY,KAAK,YAAY,CAAC;AAAA,EACtD,OAAO;AACL,OAAG,gBAAgB,IAAI;AAAA,EACzB;AACF;AAKA,SAAS,oBAAoB,IAAI,MAAM;AAErC,QAAM,YAAY,GAAG,aAAa,IAAI;AACtC,SAAO,WAAW,MAAM,MAAM,KAAK,CAAC;AACtC;AAaA,IAAM,4BAA4B;AAMlC,IAAM,iCAAiC;AAEvC,IAAI,SAAS;AAMb,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B,mBAAmB,oBAAI,IAAI;AAAA;AAAA,EAE3B,qBAAqB;AAAA;AAAA,EAErB,MAAM,GAAG,QAAQ;AAAA,EACjB,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AACzD,SAAK,MAAM,OAAO,MAAM,IAAI,MAAM;AAAA,EACpC;AAAA,EACA,SAAS,aAAa,SAAS,MAAM;AACnC,QAAI,CAAC,KAAK,gBAAgB,aAAa,OAAO,GAAG;AAC/C;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,OAAO,YAAY,UAAU;AAE/B,mBAAa,SAAS,KAAK,GAAG;AAC9B,WAAK,iBAAiB,IAAI,KAAK;AAAA,QAC7B,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,CAAC,KAAK,iBAAiB,IAAI,GAAG,GAAG;AAC1C,WAAK,sBAAsB,SAAS,IAAI;AAAA,IAC1C;AACA,QAAI,CAAC,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACxD,WAAK,qBAAqB,aAAa,GAAG;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,kBAAkB,aAAa,SAAS,MAAM;AAC5C,QAAI,CAAC,WAAW,CAAC,KAAK,eAAe,WAAW,GAAG;AACjD;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACvD,WAAK,wBAAwB,aAAa,GAAG;AAAA,IAC/C;AAGA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAI,qBAAqB,kBAAkB,mBAAmB,GAAG;AAC/D,aAAK,sBAAsB,GAAG;AAAA,MAChC;AAAA,IACF;AACA,QAAI,KAAK,oBAAoB,WAAW,WAAW,GAAG;AACpD,WAAK,mBAAmB,OAAO;AAC/B,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,oBAAoB,KAAK,UAAU,iBAAiB,IAAI,8BAA8B,KAAK,KAAK,GAAG,IAAI;AAC7G,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,WAAK,kCAAkC,kBAAkB,CAAC,CAAC;AAC3D,wBAAkB,CAAC,EAAE,gBAAgB,8BAA8B;AAAA,IACrE;AACA,SAAK,oBAAoB,OAAO;AAChC,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS,MAAM;AACnC,UAAM,iBAAiB,KAAK,UAAU,cAAc,KAAK;AACzD,iBAAa,gBAAgB,KAAK,GAAG;AACrC,mBAAe,cAAc;AAC7B,QAAI,MAAM;AACR,qBAAe,aAAa,QAAQ,IAAI;AAAA,IAC1C;AACA,SAAK,yBAAyB;AAC9B,SAAK,mBAAmB,YAAY,cAAc;AAClD,SAAK,iBAAiB,IAAI,OAAO,SAAS,IAAI,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB,KAAK;AACzB,SAAK,iBAAiB,IAAI,GAAG,GAAG,gBAAgB,OAAO;AACvD,SAAK,iBAAiB,OAAO,GAAG;AAAA,EAClC;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,UAAM,qBAAqB;AAC3B,UAAM,mBAAmB,KAAK,UAAU,iBAAiB,IAAI,kBAAkB,qBAAqB;AACpG,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAKhD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,UAAM,oBAAoB,KAAK,UAAU,cAAc,KAAK;AAK5D,sBAAkB,MAAM,aAAa;AAGrC,sBAAkB,UAAU,IAAI,kBAAkB;AAClD,sBAAkB,UAAU,IAAI,qBAAqB;AACrD,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,wBAAkB,aAAa,YAAY,QAAQ;AAAA,IACrD;AACA,SAAK,UAAU,KAAK,YAAY,iBAAiB;AACjD,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,kCAAkC,SAAS;AAEzC,UAAM,uBAAuB,oBAAoB,SAAS,kBAAkB,EAAE,OAAO,QAAM,GAAG,QAAQ,yBAAyB,KAAK,CAAC;AACrI,YAAQ,aAAa,oBAAoB,qBAAqB,KAAK,GAAG,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAAS,KAAK;AACjC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AAGvD,wBAAoB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACpF,YAAQ,aAAa,gCAAgC,KAAK,GAAG;AAC7D,sBAAkB;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,SAAS,KAAK;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,sBAAkB;AAClB,2BAAuB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACvF,YAAQ,gBAAgB,8BAA8B;AAAA,EACxD;AAAA;AAAA,EAEA,6BAA6B,SAAS,KAAK;AACzC,UAAM,eAAe,oBAAoB,SAAS,kBAAkB;AACpE,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAM,YAAY,qBAAqB,kBAAkB,eAAe;AACxE,WAAO,CAAC,CAAC,aAAa,aAAa,QAAQ,SAAS,KAAK;AAAA,EAC3D;AAAA;AAAA,EAEA,gBAAgB,SAAS,SAAS;AAChC,QAAI,CAAC,KAAK,eAAe,OAAO,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,WAAW,OAAO,YAAY,UAAU;AAI1C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,WAAW,OAAO,KAAK,GAAG,OAAO,GAAG,KAAK;AAChE,UAAM,YAAY,QAAQ,aAAa,YAAY;AAGnD,WAAO,iBAAiB,CAAC,aAAa,UAAU,KAAK,MAAM,iBAAiB;AAAA,EAC9E;AAAA;AAAA,EAEA,eAAe,SAAS;AACtB,WAAO,QAAQ,aAAa,KAAK,UAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,SAAS,OAAO,SAAS,MAAM;AAC7B,SAAO,OAAO,YAAY,WAAW,GAAG,QAAQ,EAAE,IAAI,OAAO,KAAK;AACpE;AAEA,SAAS,aAAa,SAAS,WAAW;AACxC,MAAI,CAAC,QAAQ,IAAI;AACf,YAAQ,KAAK,GAAG,yBAAyB,IAAI,SAAS,IAAI,QAAQ;AAAA,EACpE;AACF;AA2FA,IAAM,wBAAN,cAAoC,UAAU;AAAA,EAC5C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,kBAAkB,SAAS,IAAI;AAAA,IACtC,OAAO;AACL,WAAK,kBAAkB,WAAW,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,YAAY,UAAU,UAAU,SAAS,WAAW,mBAAmB,gBAAgB,QAAQ,UAAU;AACvG,UAAM,UAAU,UAAU,SAAS,WAAW,OAAO,OAAO,QAAQ;AACpE,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,SAAS,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,kBAAkB,WAAW,IAAI;AACtC,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe,aAAa,IAAI;AACrC,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,eAAe,WAAW,IAAI;AACnC,SAAK,cAAc,KAAK;AAAA,EAC1B;AACF;AAMA,IAAM,sCAAN,MAA0C;AAAA;AAAA,EAExC,YAAY;AAAA;AAAA,EAEZ,aAAa,WAAW;AAEtB,QAAI,KAAK,WAAW;AAClB,gBAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AAAA,IACvE;AACA,SAAK,YAAY,OAAK,KAAK,WAAW,WAAW,CAAC;AAClD,cAAU,QAAQ,kBAAkB,MAAM;AACxC,gBAAU,UAAU,iBAAiB,SAAS,KAAK,WAAW,IAAI;AAAA,IACpE,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,WAAW,WAAW;AACpB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,cAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AACrE,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,WAAW,OAAO;AAC3B,UAAM,SAAS,MAAM;AACrB,UAAM,gBAAgB,UAAU;AAGhC,QAAI,UAAU,CAAC,cAAc,SAAS,MAAM,KAAK,CAAC,OAAO,UAAU,sBAAsB,GAAG;AAI1F,iBAAW,MAAM;AAEf,YAAI,UAAU,WAAW,CAAC,cAAc,SAAS,UAAU,UAAU,aAAa,GAAG;AACnF,oBAAU,0BAA0B;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAGhF,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA;AAAA,EAGrB,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,WAAW;AAElB,SAAK,kBAAkB,KAAK,gBAAgB,OAAO,QAAM,OAAO,SAAS;AACzE,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,QAAQ;AAChB,YAAM,MAAM,SAAS,CAAC,EAAE,SAAS;AAAA,IACnC;AACA,UAAM,KAAK,SAAS;AACpB,cAAU,QAAQ;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,cAAU,SAAS;AACnB,UAAM,QAAQ,KAAK;AACnB,UAAM,IAAI,MAAM,QAAQ,SAAS;AACjC,QAAI,MAAM,IAAI;AACZ,YAAM,OAAO,GAAG,CAAC;AACjB,UAAI,MAAM,QAAQ;AAChB,cAAM,MAAM,SAAS,CAAC,EAAE,QAAQ;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,gBAAgB,OAAO,2BAA2B;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,iBAAiB,iBAAiB,IAAI,oCAAoC;AAAA,EACjF;AAAA,EACA,OAAO,SAAS,SAAS;AAAA,IACvB,OAAO;AAAA,EACT,GAAG;AACD,QAAI;AACJ,QAAI,OAAO,WAAW,WAAW;AAC/B,qBAAe;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,IACjB;AACA,WAAO,IAAI,sBAAsB,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,mBAAmB,KAAK,gBAAgB,cAAc,KAAK,SAAS;AAAA,EAClK;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,8BAA6B;AAAA,IACtC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACxiBH,IAAM,MAAM,CAAC,uBAAuB,EAAE;AACtC,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,IAAI;AAAA,EACrC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,iBAAiB,EAAE,YAAY,OAAO,aAAa;AAAA,EAClF;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AACjH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,WAAW,OAAO,qBAAqB;AAC1C,IAAG,YAAY,oCAAoC,OAAO,QAAQ,EAAE,8BAA8B,OAAO,OAAO,EAAE,8BAA8B,OAAO,OAAO,EAAE,mCAAmC,OAAO,OAAO;AACjN,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,gBAAgB,EAAE,YAAY,OAAO,YAAY;AAAA,EAChF;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,4DAA4D,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG,4DAA4D,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,4DAA4D,GAAG,GAAG,QAAQ,CAAC;AAC3U,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,iBAAiB,OAAO,oBAAoB,IAAI,EAAE;AAC1E,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,WAAW,OAAO,cAAc,IAAI,CAAC;AAC7D,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,gBAAgB,OAAO,mBAAmB,IAAI,EAAE;AAAA,EAC1E;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,4DAA4D,GAAG,GAAG,MAAM,CAAC;AAAA,EAC1K;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,UAAU,IAAI,CAAC;AAAA,EACzC;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,YAAY,EAAE,YAAY,OAAO,QAAQ;AAAA,EACxE;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAAC;AAC9E,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,8BAA8B,CAAC;AAAA,EACjD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,MAAM;AAAA,EACvC;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,8BAA8B,CAAC;AAChI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,YAAY,EAAE,YAAY,OAAO,QAAQ;AACtE,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,aAAa,IAAI,EAAE;AAAA,EAC7C;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,4DAA4D,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,4DAA4D,GAAG,GAAG,QAAQ,CAAC;AAAA,EACrQ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,cAAc,OAAO,YAAY,OAAO,eAAe,IAAI,EAAE;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,kBAAkB;AACpD,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,YAAY,OAAO,gBAAgB,OAAO,aAAa,IAAI,EAAE;AAAA,EACvF;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,UAAU,GAAG,8BAA8B,CAAC;AAC/C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO,MAAM;AAAA,EACvC;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AACjB,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,QAAQ,EAAE;AAAA,EAC/F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,WAAW,CAAC,OAAO,WAAW,OAAO,aAAa,IAAI,EAAE;AAAA,EACnF;AACF;AACA,IAAM,MAAM,CAAC,UAAU;AACvB,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,IAAG,WAAW,SAAS,SAAS,0DAA0D,QAAQ;AAChG,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,WAAW,MAAM,CAAC;AAAA,IACzD,CAAC,EAAE,SAAS,SAAS,0DAA0D,QAAQ;AACrF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,MAAM,CAAC;AAAA,IAC9C,CAAC,EAAE,WAAW,SAAS,4DAA4D,QAAQ;AACzF,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,WAAW,MAAM,CAAC;AAAA,IAC3D,CAAC,EAAE,SAAS,SAAS,0DAA0D,QAAQ;AACrF,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,WAAW,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,eAAe,OAAO,EAAE,YAAY,OAAO,QAAQ;AAAA,EAC5F;AACF;AACA,IAAM,MAAM,CAAC,CAAC,CAAC,YAAY,YAAY,EAAE,CAAC,CAAC;AAC3C,IAAM,MAAM,CAAC,oBAAoB;AACjC,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,KAAK,OAAO,UAAU,EAAE;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,IAAI,WAAW,OAAO;AACpB,UAAM,iBAAiB,UAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,KAAK;AAClH,QAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,WAAK,WAAW;AAAA,IAClB,WAAW,eAAe,KAAK,GAAG;AAChC,WAAK,WAAW;AAChB,WAAK,UAAU,MAAM;AACrB,WAAK,UAAU,MAAM;AACrB,WAAK,YAAY,KAAK,aAAa;AACnC,WAAK,YAAY,KAAK,aAAa;AAAA,IACrC;AAAA,EACF;AAAA,EACA,mBAAmB,QAAQ,OAAO;AAChC,SAAK,wBAAwB;AAG7B,QAAI,CAAC,KAAK,kBAAkB;AAC1B;AAAA,IACF;AACA,UAAM,WAAW,KAAK;AACtB,UAAM,QAAQ,SAAS;AAEvB,QAAI,CAAC,SAAS,KAAK,YAAY,KAAK,mBAAmB,UAAU,KAAK,eAAe;AACnF;AAAA,IACF;AACA,UAAM,kBAAkB,SAAS;AAMjC,aAAS,UAAU,IAAI,gCAAgC;AACvD,aAAS,cAAc;AACvB,QAAI,SAAS,KAAK,OAAO,SAAS,eAAe,KAAK,YAAY,KAAK,gBAAgB,IAAI,KAAK,mBAAmB,KAAK;AACxH,QAAI,KAAK,cAAc,QAAQ,SAAS,KAAK,WAAW;AACtD,eAAS,KAAK;AAAA,IAChB;AACA,QAAI,KAAK,cAAc,QAAQ,SAAS,KAAK,WAAW;AACtD,eAAS,KAAK;AAAA,IAChB;AAEA,aAAS,MAAM,SAAS,GAAG,MAAM;AACjC,aAAS,UAAU,OAAO,gCAAgC;AAC1D,aAAS,cAAc;AAGvB,QAAI,OAAO,0BAA0B,aAAa;AAChD,WAAK,OAAO,kBAAkB,MAAM,sBAAsB,MAAM;AAC9D,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AAOJ,YAAI,CAAC,KAAK,SAAS,aAAa,SAAS,kBAAkB,UAAU;AACnE,mBAAS,kBAAkB,gBAAgB,YAAY;AAAA,QACzD;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,SAAK,gBAAgB;AACrB,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,oBAAoB,KAAK,CAAC,KAAK,GAAG,YAAY;AACrD;AAAA,IACF;AAEA,UAAM,gBAAgB,KAAK,GAAG,UAAU,KAAK;AAC7C,kBAAc,OAAO;AAIrB,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,aAAa;AACjC,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,YAAY;AAMhC,kBAAc,MAAM,WAAW;AAC/B,SAAK,GAAG,WAAW,YAAY,aAAa;AAC5C,SAAK,mBAAmB,cAAc,eAAe,KAAK;AAC1D,SAAK,GAAG,WAAW,YAAY,aAAa;AAE5C,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,YAAY,KAAK,aAAa;AAAA,EACrC;AAAA,EACA,eAAe;AACb,UAAM,YAAY,KAAK,WAAW,KAAK,mBAAmB,KAAK,UAAU,KAAK,mBAAmB,KAAK,WAAW;AACjH,QAAI,cAAc,MAAM;AACtB,WAAK,GAAG,MAAM,YAAY,GAAG,SAAS;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,UAAM,YAAY,KAAK,WAAW,KAAK,mBAAmB,KAAK,UAAU,KAAK,mBAAmB,KAAK,WAAW;AACjH,QAAI,cAAc,MAAM;AACtB,WAAK,GAAG,MAAM,YAAY,GAAG,SAAS;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,EAEnB;AAAA,EACA,YAAY,QAAQ,UAAU,eAAe;AAC3C,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,YAAY,KAAK,SAAS,WAAW;AAC5C,WAAK,mBAAmB;AACxB,WAAK,cAAc,UAAU,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,IAAI,CAAC;AAAA,IAC7G;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,YAAY;AACV,QAAI,KAAK,YAAY,KAAK,SAAS,WAAW;AAC5C,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,MAAM,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,eAAe,CAAC;AAAA,EACpK;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,cAAc,EAAE,CAAC;AAAA,IAC1C,WAAW,CAAC,QAAQ,GAAG;AAAA,IACvB,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,+CAA+C;AAC7E,iBAAO,IAAI,iBAAiB;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA;AAAA;AAAA,QAGJ,MAAM;AAAA,QACN,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAA6B;AAAA,EAChE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,EACvC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,EACvC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,OAAO;AAAA,EACP,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,IAC3C,UAAU;AAAA,IACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,yBAAyB,IAAI,SAAS,OAAO,EAAE,oBAAoB,IAAI,SAAS,QAAQ,EAAE,oBAAoB,IAAI,SAAS,QAAQ;AAAA,MACpJ;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,IACrD,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,mDAAmD,GAAG,GAAG,gBAAgB,CAAC;AACpK,QAAG,aAAa,CAAC;AAAA,MACnB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,OAAO,IAAI,EAAE;AAClC,QAAG,UAAU;AACb,QAAG,WAAW,0BAA0B,IAAI,QAAQ;AAAA,MACtD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAmB,iBAAiB,gBAAqB,+BAA+B;AAAA,IACvG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,MAAM;AAAA,QACJ,iCAAiC;AAAA,QACjC,4BAA4B;AAAA,QAC5B,4BAA4B;AAAA,MAC9B;AAAA,MACA,SAAS,CAAC,cAAc,cAAc;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,IAAI,WAAW;AACb,QAAI,KAAK,aAAa,KAAK,UAAU,aAAa,MAAM;AACtD,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,EACZ,YAAY,IAAI,QAAQ;AAAA,EACxB,MAAM;AAAA;AAAA,EAEN,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY,CAAC;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa,CAAC;AAAA,EACd,YAAY,OAAO,WAAW;AAAA,IAC5B,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,SAAS,MAAM;AACzB,QAAI,KAAK,aAAa;AACpB,aAAO,KAAK,YAAY;AAAA,IAC1B;AACA,WAAO,KAAK,KAAK;AAAA,EACnB,CAAC;AAAA,EACD,OAAO,OAAO,KAAK,MAAM;AAAA,EACzB,cAAc,OAAO,uBAAuB;AAAA,IAC1C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,gBAAgB;AAAA,EAClC,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,wBAAwB,OAAO,uBAAuB;AAAA,IACpD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,UAAU,YAAY,UAAU,gBAAgB;AAC1D,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,qBAAqB,kBAAkB,KAAK,qBAAqB,CAAC,KAAK,QAAQ;AAClF,aAAO,IAAI,WAAW,IAAI,UAAU,IAAI,gBAAgB,IAAI;AAAA,IAC9D,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC;AAAA,MACvC;AAAA,MACA;AAAA,IACF,MAAM;AACJ,WAAK,gBAAgB,QAAQ,WAAW;AAAA,IAC1C,CAAC;AACD,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,eAAe,KAAK,OAAO,MAAM,KAAK,UAAU,aAAa,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC3H,aAAK,UAAU,KAAK,KAAK,UAAU,QAAQ;AAAA,MAC7C,CAAC;AAAA,IACH;AACA,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,UAAU;AACZ,WAAK,UAAU,KAAK,KAAK,QAAQ;AAAA,IACnC;AACA,QAAI,UAAU;AACZ,WAAK,gBAAgB,KAAK,UAAU,KAAK,WAAW;AAAA,IACtD;AACA,QAAI,QAAQ;AACV,WAAK,KAAK,IAAI,OAAO,YAAY;AAAA,IACnC;AAAA,EACF;AAAA,EACA,gBAAgB,QAAQ,aAAa;AAEnC,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,SAAK,YAAY,oBAAoB,KAAK,WAAW,QAAQ,WAAW;AACxE,WAAO,KAAK,KAAK,SAAS,EAAE,QAAQ,CAAAC,YAAU;AAC5C,UAAI,KAAK,UAAUA,OAAM,GAAG;AAC1B,aAAK,SAAS,SAAS,KAAK,WAAW,eAAeA,OAAM;AAAA,MAC9D,OAAO;AACL,aAAK,SAAS,YAAY,KAAK,WAAW,eAAeA,OAAM;AAAA,MACjE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,eAAe,CAAC,CAAC,KAAK,uBAAuB;AAErE,WAAK,SAAS,MAAM;AACpB,WAAK,cAAc;AACnB;AAAA,IACF;AACA,SAAK,cAAc,KAAK,eAAe,KAAK,SAAS,gBAAgB,+BAA+B;AACpG,SAAK,YAAY,SAAS,cAAc,UAAU,IAAI,kBAAkB;AACxE,SAAK,YAAY,SAAS,SAAS,KAAK;AACxC,SAAK,YAAY,SAAS,WAAW;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAuB,cAAc,CAAC;AAAA,EAClN;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,YAAY,EAAE,GAAG,CAAC,YAAY,YAAY,EAAE,CAAC;AAAA,IACnE,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI,YAAY,IAAI;AAC/C,QAAG,YAAY,sBAAsB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,YAAY,EAAE,gBAAgB,IAAI,UAAU,MAAM,OAAO,EAAE,gBAAgB,IAAI,UAAU,MAAM,OAAO,EAAE,iBAAiB,IAAI,QAAQ,KAAK,EAAE,yBAAyB,IAAI,aAAa;AAAA,MACvQ;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,QAAQ;AAAA,MACR,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU;AAAA,MACV,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,mBAAmB,CAAC,kBAAkB;AAAA,MAClD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,CAAC,GAAM,wBAAwB,CAAM,2BAA2B,CAAC,GAAM,oBAAoB;AAAA,EAC9F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,yBAAyB;AAAA,QACzB,iCAAiC;AAAA,MACnC;AAAA,MACA,gBAAgB,CAAC,2BAA2B;AAAA,MAC5C,WAAW,CAAC,kBAAkB;AAAA,QAC5B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0CAAN,MAAM,yCAAwC;AAAA,EAC5C;AAAA,EACA,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,gDAAgD,mBAAmB;AACxF,WAAO,KAAK,qBAAqB,0CAA4C,kBAAqB,UAAU,CAAC;AAAA,EAC/G;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,YAAY,EAAE,GAAG,CAAC,kBAAkB,YAAY,EAAE,CAAC;AAAA,EACpF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yCAAyC,CAAC;AAAA,IAChH,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,EACV,WAAW;AAAA,EACX,MAAM;AAAA;AAAA,EAEN,YAAY;AAAA,EACZ,iBAAiB,CAAC;AAAA,EAClB,iBAAiB,CAAC;AAAA,EAClB,wBAAwB,CAAC;AAAA,EACzB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,WAAW,IAAI,QAAQ;AAAA,EACvB,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,wBAAwB,OAAO,uBAAuB;AAAA,IACpD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,cAAc,YAAY,UAAU,KAAK,gBAAgB;AACnE,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB,QAAQ,UAAQ,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,qBAAqB,kBAAkB,KAAK,qBAAqB,CAAC,KAAK,QAAQ;AAClF,aAAO,IAAI,WAAW,IAAI,UAAU,IAAI,gBAAgB,IAAI;AAAA,IAC9D,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC;AAAA,MACvC;AAAA,MACA;AAAA,IACF,MAAM;AACJ,WAAK,gBAAgB,QAAQ,WAAW;AAAA,IAC1C,CAAC;AACD,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,WAAK,UAAU,CAAC,CAAC;AACjB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,wBAAwB;AAC7B,UAAM,qBAAqB,KAAK,uBAAuB,QAAQ,KAAK,UAAU,KAAK,sBAAsB,CAAC;AAC1G,uBAAmB,KAAK,UAAU,UAAQ,MAAM,GAAG,CAAC,oBAAoB,GAAG,KAAK,IAAI,WAAS,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,MAAM,kBAAkB,GAAG,IAAI,UAAQ,KAAK,KAAK,WAAS,MAAM,QAAQ,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACrP,WAAK,WAAW;AAChB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ;AACV,WAAK,wBAAwB;AAC7B,WAAK,UAAU,KAAK,WAAW;AAC/B,WAAK,UAAU,KAAK,WAAW;AAAA,IACjC;AACA,QAAI,YAAY,YAAY,gBAAgB,cAAc;AACxD,WAAK,UAAU,CAAC,EAAE,KAAK,YAAY,KAAK,YAAY,KAAK,gBAAgB,KAAK;AAAA,IAChF;AACA,QAAI,gBAAgB,iBAAiB,oBAAoB,mBAAmB;AAC1E,WAAK,UAAU,CAAC,EAAE,KAAK,gBAAgB,KAAK,iBAAiB,KAAK,oBAAoB,KAAK;AAC3F,WAAK,uBAAuB,cAAc,KAAK,KAAK,OAAO;AAAA,IAC7D;AACA,QAAI,UAAU;AACZ,WAAK,gBAAgB,KAAK,UAAU,KAAK,WAAW;AAAA,IACtD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,eAAe,KAAK,UAAU;AAChD,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,gBAAgB,QAAQ,aAAa;AAEnC,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,aAAa,CAAC,CAAC,UAAU;AAC9B,UAAM,YAAY,CAAC,EAAE,KAAK,YAAY,KAAK,YAAY,KAAK,gBAAgB,KAAK;AACjF,SAAK,UAAU,aAAa,CAAC,KAAK,WAAW;AAC7C,SAAK,wBAAwB,KAAK,WAAW,KAAK,aAAa,KAAK,iBAAiB,oBAAoB,GAAG,KAAK,SAAS,kBAAkB,QAAQ,WAAW,IAAI,CAAC;AACpK,SAAK,IAAI,aAAa;AAEtB,SAAK,iBAAiB,oBAAoB,GAAG,KAAK,SAAS,kBAAkB,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,QAAQ,WAAW;AAC3I,SAAK,iBAAiB,oBAAoB,GAAG,KAAK,SAAS,kBAAkB,KAAK,UAAU,SAAS,IAAI,KAAK,UAAU,cAAc,KAAK;AAC3I,UAAM,YAAY,kCACb,KAAK,iBACL,KAAK;AAEV,WAAO,KAAK,SAAS,EAAE,QAAQ,CAAAA,YAAU;AACvC,UAAI,UAAUA,OAAM,GAAG;AACrB,aAAK,SAAS,SAAS,KAAK,WAAW,eAAeA,OAAM;AAAA,MAC9D,OAAO;AACL,aAAK,SAAS,YAAY,KAAK,WAAW,eAAeA,OAAM;AAAA,MACjE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAA0B,kBAAuB,YAAY,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,cAAc,CAAC;AAAA,EACjQ;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,gBAAgB,SAAS,qCAAqC,IAAI,KAAK,UAAU;AAC/E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAAA,MACjD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,yBAAyB;AAAA,MAC5E;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,2BAA2B,IAAI,SAAS,EAAE,iCAAiC,IAAI,QAAQ,EAAE,oBAAoB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,QAAQ,KAAK,EAAE,uBAAuB,IAAI,YAAY,IAAI,OAAO,EAAE,0BAA0B,IAAI,YAAY,IAAI,OAAO,EAAE,2BAA2B,IAAI,OAAO,EAAE,+BAA+B,IAAI,QAAQ,KAAK,EAAE,8BAA8B,IAAI,WAAW,IAAI,OAAO,EAAE,8BAA8B,IAAI,WAAW,IAAI,OAAO,EAAE,2BAA2B,IAAI,WAAW,CAAC,IAAI,OAAO,EAAE,+BAA+B,IAAI,QAAQ,KAAK,EAAE,mCAAmC,IAAI,WAAW,IAAI,OAAO,EAAE,oCAAoC,IAAI,WAAW,IAAI,QAAQ,EAAE,8BAA8B,IAAI,WAAW,CAAC,IAAI,WAAW,IAAI,OAAO,EAAE,8BAA8B,IAAI,WAAW,CAAC,IAAI,WAAW,IAAI,OAAO,EAAE,mBAAmB,CAAC,IAAI,WAAW,CAAC,IAAI,OAAO,EAAE,uBAAuB,IAAI,QAAQ,KAAK,EAAE,sBAAsB,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,IAAI,OAAO,EAAE,sBAAsB,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,IAAI,OAAO;AAAA,MAC7kC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,eAAe;AAAA,MACf,cAAc;AAAA,MACd,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,IAC3D;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC,uBAAuB;AAAA,MACvD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,CAAC,GAAM,wBAAwB,CAAM,2BAA2B,CAAC,GAAM,oBAAoB;AAAA,IAC5F,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,qBAAqB,iBAAiB,GAAG,CAAC,uBAAuB,IAAI,QAAQ,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,2BAA2B,GAAG,oCAAoC,8BAA8B,8BAA8B,mCAAmC,OAAO,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,uBAAuB,IAAI,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,CAAC,uBAAuB,IAAI,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,uBAAuB,IAAI,QAAQ,QAAQ,CAAC;AAAA,IAC3lB,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,8CAA8C,GAAG,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,8CAA8C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAC3V;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,UAAU,IAAI,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,2BAA2B,kBAAkB,+BAA+B;AAAA,IAC3F,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,2BAA2B,kBAAkB,+BAA+B;AAAA,MACtF,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,uBAAuB;AAAA,QACjC,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0DV,MAAM;AAAA,QACJ,mCAAmC;AAAA,QACnC,yCAAyC;AAAA,QACzC,4BAA4B;AAAA,QAC5B,gCAAgC;AAAA,QAChC,+BAA+B;AAAA,QAC/B,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,uCAAuC;AAAA,QACvC,sCAAsC;AAAA,QACtC,sCAAsC;AAAA,QACtC,mCAAmC;AAAA,QACnC,uCAAuC;AAAA,QACvC,2CAA2C;AAAA,QAC3C,4CAA4C;AAAA,QAC5C,sCAAsC;AAAA,QACtC,sCAAsC;AAAA,QACtC,2BAA2B;AAAA,QAC3B,+BAA+B;AAAA,QAC/B,8BAA8B;AAAA,QAC9B,8BAA8B;AAAA,MAChC;AAAA,MACA,gBAAgB,CAAC,2BAA2B;AAAA,MAC5C,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc,WAAS;AAAA,EACvB,SAAS;AAAA,EACT;AAAA,EACA,gBAAgB,CAAC;AAAA,EACjB;AAAA,EACA,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,YAAY,aAAa,kBAAkB;AACzC,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU,GAAG,cAAc;AACrC,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,QAAQ,UAAU,GAAG;AACvB,WAAK,iBAAiB,KAAK,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA,EACA,QAAQ,OAAO,OAAO;AACpB,UAAM,eAAe,MAAM;AAC3B,UAAM,YAAY,KAAK,UAAU,QAAQ,EAAE,QAAQ,CAAC;AACpD,QAAI,aAAa,SAAS,WAAW;AACnC,gBAAU,cAAc,MAAM;AAAA,IAChC,WAAW,CAAC,WAAW;AACrB,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,UAAM,eAAe,MAAM;AAC3B,iBAAa,OAAO;AAAA,EACtB;AAAA,EACA,UAAU,OAAO,OAAO;AACtB,UAAM,gBAAgB,KAAK,UAAU,QAAQ,EAAE,QAAQ,CAAC;AACxD,QAAI,MAAM,YAAY,WAAW;AAC/B,YAAM,eAAe;AACrB,WAAK,cAAc,KAAK,IAAI;AAC5B,WAAK,SAAS,GAAG,KAAK,EAAE,SAAS,IAAI;AAAA,QACnC,WAAW;AAAA,MACb,CAAC;AACD,UAAI,eAAe;AACjB,aAAK,eAAe,QAAQ,CAAC;AAAA,MAC/B;AACA,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,CAAC,OAAO;AACV,WAAK,SAAS,MAAM;AACpB;AAAA,IACF;AACA,UAAM,gBAAgB,MAAM,MAAM,EAAE;AACpC,SAAK,gBAAgB;AACrB,kBAAc,QAAQ,CAAC,KAAK,MAAM;AAChC,YAAM,iBAAiB,KAAK,YAAY,GAAG;AAC3C,YAAMC,SAAQ,KAAK,SAAS,KAAK,SAAS;AAC1C,WAAK,SAAS,GAAG,CAAC,EAAE,SAASA,QAAO;AAAA,QAClC,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,YAAY;AAC3B,QAAI,YAAY;AACd,WAAK,SAAS,QAAQ;AAAA,IACxB,OAAO;AACL,WAAK,SAAS,OAAO;AAAA,IACvB;AAAA,EACF;AAAA,EACA,QAAQ,OAAO,OAAO;AACpB,UAAM,aAAa,MAAM,eAAe,QAAQ,MAAM,KAAK;AAC3D,QAAI,CAAC,WAAY;AACjB,QAAI,eAAe;AACnB,eAAW,QAAQ,WAAW,MAAM,EAAE,GAAG;AACvC,UAAI,eAAe,KAAK,UAAU;AAChC,cAAM,gBAAgB,KAAK,YAAY,IAAI;AAC3C,aAAK,cAAc,YAAY,IAAI;AACnC,cAAM,cAAc,KAAK,SAAS,KAAK,SAAS;AAChD,aAAK,SAAS,GAAG,YAAY,EAAE,SAAS,aAAa;AAAA,UACnD,WAAW;AAAA,QACb,CAAC;AACD;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAAA,IACF;AACA,UAAM,eAAe;AACrB,SAAK,eAAe,YAAY;AAChC,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB;AAChB,SAAK,WAAW,KAAK,YAAY,MAAM,CAAC,CAAC;AACzC,SAAK,gBAAgB,IAAI,MAAM,KAAK,QAAQ,EAAE,KAAK,EAAE;AACrD,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,KAAK;AACtC,YAAM,UAAU,KAAK,YAAY,YAAY,QAAQ,IAAI,CAAC,WAAW,QAAQ,CAAC;AAC9E,cAAQ,aAAa,KAAK,IAAI,WAAS;AACrC,cAAM,gBAAgB,KAAK,YAAY,KAAK;AAC5C,aAAK,cAAc,CAAC,IAAI;AACxB,gBAAQ,SAAS,KAAK,UAAU,eAAe;AAAA,UAC7C,WAAW;AAAA,QACb,CAAC;AACD,aAAK,UAAU;AAAA,MACjB,CAAC,GAAG,UAAU,KAAK,gBAAgB,CAAC,EAAE,UAAU;AAChD,WAAK,SAAS,KAAK,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,YAAY;AACV,UAAM,SAAS,KAAK,cAAc,KAAK,EAAE;AACzC,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,MAAM;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,KAAK,UAAU,QAAQ;AAC7C,QAAI,SAAS,cAAc,OAAQ,SAAQ,cAAc,SAAS;AAClE,kBAAc,KAAK,EAAE,cAAc,OAAO;AAAA,EAC5C;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,kBAAuB,WAAW,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,EACzI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,SAAS,0BAA0B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,SAAS;AAAA,IACxB,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,QAAQ;AAAA,MACR,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU;AAAA,MACV,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,oBAAmB;AAAA,MACjD,OAAO;AAAA,IACT,GAAG,gBAAgB,CAAC,GAAM,oBAAoB;AAAA,IAC9C,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,YAAY,IAAI,QAAQ,QAAQ,aAAa,KAAK,QAAQ,KAAK,GAAG,iBAAiB,GAAG,UAAU,eAAe,UAAU,GAAG,CAAC,YAAY,IAAI,QAAQ,QAAQ,aAAa,KAAK,QAAQ,KAAK,GAAG,iBAAiB,GAAG,SAAS,SAAS,WAAW,SAAS,UAAU,eAAe,UAAU,CAAC;AAAA,IACzT,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,iBAAiB,GAAG,oCAAoC,GAAG,GAAG,SAAS,GAAM,sBAAsB;AAAA,MACxG;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,SAAS,QAAQ;AAAA,MACrC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAkB,qBAA0B,sBAA2B,iBAAsB,oBAAyB,oBAAoB;AAAA,IACzJ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,mBAAmB;AAAA,QACjD,OAAO;AAAA,MACT,GAAG,gBAAgB;AAAA,MACnB,SAAS,CAAC,kBAAkB,mBAAmB;AAAA,IACjD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB,0BAA0B,OAAK,EAAE;AAAA,EACjC,cAAc,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AAAA,EACnD,gBAAgB,IAAI,QAAQ;AAAA,EAC5B,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,UAAU,YAAY;AAChC,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,oBAAoB,UAAU,GAAG;AACzC,YAAM,IAAI,MAAM,wEAAwE;AAAA,IAC1F;AACA,QAAI,KAAK,iBAAiB,WAAW;AACnC,YAAM,eAAe,KAAK,iBAAiB,UAAU,gBAAgB;AACrE,YAAM,cAAc,KAAK,aAAa,EAAE,KAAK,UAAU,KAAK,QAAQ,GAAG,IAAI,MAAM,KAAK,iBAAiB,UAAU,KAAK,GAAG,UAAU,KAAK,iBAAiB,UAAU,KAAK,CAAC,EAAE,UAAU,WAAS;AAC5L,aAAK,aAAa,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM,aAAa,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI;AACrD,UAAM,eAAe,KAAK,wBAAwB,UAAU;AAC5D,UAAM,YAAY,KAAK,YAAY,cAAc,KAAK,mBAAmB;AACzE,SAAK,SAAS,aAAa,KAAK,WAAW,eAAe,cAAc,SAAS;AAAA,EACnF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAC5B,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAAA,EACpI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAAA,MACjD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,+BAA+B;AAAA,IAC9C,QAAQ;AAAA,MACN,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,eAAe;AAAA,MACtF,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,0BAA0B,kBAAkB,uBAAuB,qBAAqB,2BAA2B,yCAAyC,mBAAmB;AAAA,IACzL,SAAS,CAAC,0BAA0B,kBAAkB,uBAAuB,qBAAqB,yCAAyC,mBAAmB;AAAA,EAChK,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,uBAAuB,2BAA2B,mBAAmB;AAAA,EACjF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,0BAA0B,kBAAkB,uBAAuB,qBAAqB,2BAA2B,yCAAyC,mBAAmB;AAAA,MACzL,SAAS,CAAC,0BAA0B,kBAAkB,uBAAuB,qBAAqB,yCAAyC,mBAAmB;AAAA,IAChK,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["document", "FocusMonitorDetectionMode", "window", "window", "HighContrastMode", "status", "value"]}